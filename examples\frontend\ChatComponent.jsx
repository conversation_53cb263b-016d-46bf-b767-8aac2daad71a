import React, { useState, useEffect, useRef } from 'react';
import { ChatWebSocket, ChatAPIClient } from './chat-client.js';

const ChatComponent = ({ token, baseUrl = 'http://localhost:8080' }) => {
  const [session, setSession] = useState(null);
  const [messages, setMessages] = useState([]);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [recommendations, setRecommendations] = useState([]);
  const [error, setError] = useState(null);
  
  const chatWS = useRef(null);
  const apiClient = useRef(null);

  useEffect(() => {
    apiClient.current = new ChatAPIClient(baseUrl, token);
    initializeChat();
    
    return () => {
      if (chatWS.current) {
        chatWS.current.disconnect();
      }
    };
  }, [token, baseUrl]);

  const initializeChat = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Create new session
      const newSession = await apiClient.current.createSession();
      setSession(newSession);
      
      // Connect WebSocket
      connectWebSocket(newSession.session_id);
      
    } catch (err) {
      setError(`Failed to initialize chat: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const connectWebSocket = (sessionId) => {
    chatWS.current = new ChatWebSocket(
      sessionId,
      token,
      handleMessage,
      handleError,
      handleClose
    );
    
    chatWS.current.connect();
  };

  const handleMessage = (message) => {
    setMessages(prev => [...prev, message]);
    
    switch (message.type) {
      case 'welcome':
        // Welcome message received
        break;
        
      case 'question':
        setCurrentQuestion(message.data);
        break;
        
      case 'recommendation':
        setRecommendations(message.data.properties);
        setCurrentQuestion(null); // Chat completed
        break;
        
      case 'error':
        setError(message.error);
        break;
    }
  };

  const handleError = (error) => {
    setError(`WebSocket error: ${error.message || error}`);
    setIsConnected(false);
  };

  const handleClose = (event) => {
    setIsConnected(false);
    if (event.code !== 1000) {
      setError('Connection lost. Attempting to reconnect...');
    }
  };

  const sendResponse = (questionId, answer, answerData = null) => {
    if (chatWS.current) {
      chatWS.current.sendUserResponse(questionId, answer, answerData);
      
      // Add user message to chat
      const userMessage = {
        type: 'user_message',
        content: answer,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, userMessage]);
    }
  };

  const restartChat = async () => {
    try {
      setIsLoading(true);
      setMessages([]);
      setCurrentQuestion(null);
      setRecommendations([]);
      setError(null);
      
      if (session) {
        await apiClient.current.restartSession(session.session_id);
      } else {
        await initializeChat();
      }
    } catch (err) {
      setError(`Failed to restart chat: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const requestPropertyDetails = (propertyId) => {
    if (chatWS.current) {
      chatWS.current.requestPropertyDetails(propertyId);
    }
  };

  const renderQuestion = () => {
    if (!currentQuestion) return null;

    const { question_id, text, question_type, options, is_required } = currentQuestion;

    switch (question_type) {
      case 'single_choice':
        return (
          <div className="question-container">
            <h3>{text} {is_required && <span className="required">*</span>}</h3>
            <div className="options">
              {options.map((option, index) => (
                <button
                  key={index}
                  className="option-button"
                  onClick={() => sendResponse(question_id, option)}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        );

      case 'multiple_choice':
        return (
          <MultipleChoiceQuestion
            question={currentQuestion}
            onSubmit={(selectedOptions) => 
              sendResponse(question_id, selectedOptions.join(', '))
            }
          />
        );

      case 'range':
        return (
          <RangeQuestion
            question={currentQuestion}
            onSubmit={(min, max) => 
              sendResponse(question_id, `${min}-${max}`, { min, max })
            }
          />
        );

      case 'boolean':
        return (
          <div className="question-container">
            <h3>{text} {is_required && <span className="required">*</span>}</h3>
            <div className="options">
              <button
                className="option-button"
                onClick={() => sendResponse(question_id, 'yes')}
              >
                Yes
              </button>
              <button
                className="option-button"
                onClick={() => sendResponse(question_id, 'no')}
              >
                No
              </button>
            </div>
          </div>
        );

      case 'text':
        return (
          <TextQuestion
            question={currentQuestion}
            onSubmit={(text) => sendResponse(question_id, text)}
          />
        );

      default:
        return <div>Unsupported question type: {question_type}</div>;
    }
  };

  const renderRecommendations = () => {
    if (recommendations.length === 0) return null;

    return (
      <div className="recommendations-container">
        <h2>Property Recommendations</h2>
        <div className="recommendations-grid">
          {recommendations.map((property, index) => (
            <PropertyCard
              key={property.id || index}
              property={property}
              onDetailsClick={() => requestPropertyDetails(property.id)}
            />
          ))}
        </div>
        <button className="restart-button" onClick={restartChat}>
          Start New Search
        </button>
      </div>
    );
  };

  if (isLoading) {
    return <div className="loading">Initializing chat...</div>;
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error">{error}</div>
        <button onClick={initializeChat}>Retry</button>
      </div>
    );
  }

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h1>Property Recommendation Chat</h1>
        {session && (
          <div className="session-info">
            Session: {session.session_id.substring(0, 8)}...
          </div>
        )}
      </div>

      <div className="chat-content">
        {currentQuestion ? renderQuestion() : renderRecommendations()}
      </div>

      <div className="chat-actions">
        <button onClick={restartChat} disabled={isLoading}>
          Restart Chat
        </button>
      </div>
    </div>
  );
};

// Helper Components

const MultipleChoiceQuestion = ({ question, onSubmit }) => {
  const [selectedOptions, setSelectedOptions] = useState([]);

  const toggleOption = (option) => {
    setSelectedOptions(prev => 
      prev.includes(option)
        ? prev.filter(o => o !== option)
        : [...prev, option]
    );
  };

  return (
    <div className="question-container">
      <h3>{question.text} {question.is_required && <span className="required">*</span>}</h3>
      <div className="options">
        {question.options.map((option, index) => (
          <label key={index} className="checkbox-option">
            <input
              type="checkbox"
              checked={selectedOptions.includes(option)}
              onChange={() => toggleOption(option)}
            />
            {option}
          </label>
        ))}
      </div>
      <button
        className="submit-button"
        onClick={() => onSubmit(selectedOptions)}
        disabled={question.is_required && selectedOptions.length === 0}
      >
        Submit
      </button>
    </div>
  );
};

const RangeQuestion = ({ question, onSubmit }) => {
  const [min, setMin] = useState('');
  const [max, setMax] = useState('');

  const handleSubmit = () => {
    const minVal = parseFloat(min);
    const maxVal = parseFloat(max);
    
    if (minVal && maxVal && minVal < maxVal) {
      onSubmit(minVal, maxVal);
    }
  };

  return (
    <div className="question-container">
      <h3>{question.text} {question.is_required && <span className="required">*</span>}</h3>
      <div className="range-inputs">
        <input
          type="number"
          placeholder="Minimum"
          value={min}
          onChange={(e) => setMin(e.target.value)}
        />
        <span>to</span>
        <input
          type="number"
          placeholder="Maximum"
          value={max}
          onChange={(e) => setMax(e.target.value)}
        />
      </div>
      <button
        className="submit-button"
        onClick={handleSubmit}
        disabled={!min || !max || parseFloat(min) >= parseFloat(max)}
      >
        Submit
      </button>
    </div>
  );
};

const TextQuestion = ({ question, onSubmit }) => {
  const [text, setText] = useState('');

  const handleSubmit = () => {
    if (text.trim()) {
      onSubmit(text.trim());
    }
  };

  return (
    <div className="question-container">
      <h3>{question.text} {question.is_required && <span className="required">*</span>}</h3>
      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Enter your answer..."
        rows={3}
      />
      <button
        className="submit-button"
        onClick={handleSubmit}
        disabled={question.is_required && !text.trim()}
      >
        Submit
      </button>
    </div>
  );
};

const PropertyCard = ({ property, onDetailsClick }) => {
  return (
    <div className="property-card">
      <div className="property-header">
        <h3>{property.title}</h3>
        <div className="property-score">Score: {(property.score * 100).toFixed(0)}%</div>
      </div>
      
      <div className="property-details">
        <p><strong>Price:</strong> ₹{property.total_price?.toLocaleString()}</p>
        <p><strong>Location:</strong> {property.location?.area}, {property.location?.city}</p>
        <p><strong>Type:</strong> {property.type}</p>
        <p><strong>BHK:</strong> {property.bhk}</p>
      </div>

      {property.match_reasons && (
        <div className="match-reasons">
          <strong>Why this matches:</strong>
          <ul>
            {property.match_reasons.map((reason, index) => (
              <li key={index}>{reason}</li>
            ))}
          </ul>
        </div>
      )}

      <button className="details-button" onClick={onDetailsClick}>
        View Details
      </button>
    </div>
  );
};

export default ChatComponent;
