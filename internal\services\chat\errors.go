package chat

import "errors"

// Chat service errors
var (
	ErrSessionNotFound     = errors.New("chat session not found")
	ErrSessionExpired      = errors.New("chat session expired")
	ErrSessionInactive     = errors.New("chat session is not active")
	ErrQuestionNotFound    = errors.New("question not found")
	ErrNoQuestionsFound    = errors.New("no questions found")
	ErrNoMoreQuestions     = errors.New("no more questions available")
	ErrInvalidResponse     = errors.New("invalid response")
	ErrConversationComplete = errors.New("conversation already complete")
	ErrPropertyNotFound    = errors.New("property not found")
	ErrRecommendationFailed = errors.New("failed to generate recommendations")
)
