/**
 * Real Estate Chat System - Frontend Integration Example
 * 
 * This example demonstrates how to integrate the chat system with a React.js frontend
 */

class ChatWebSocket {
  constructor(sessionId, token, onMessage, onError, onClose) {
    this.sessionId = sessionId;
    this.token = token;
    this.onMessage = onMessage;
    this.onError = onError;
    this.onClose = onClose;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
  }

  connect() {
    const wsUrl = `ws://localhost:8080/api/v1/chat/ws/${this.sessionId}?token=${this.token}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.onError && this.onError(error);
    }
  }

  setupEventHandlers() {
    this.ws.onopen = (event) => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('Received message:', message);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.onError && this.onError(error);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      this.onClose && this.onClose(event);
      
      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.attemptReconnect();
      }
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'welcome':
        this.handleWelcome(message);
        break;
      case 'question':
        this.handleQuestion(message);
        break;
      case 'recommendation':
        this.handleRecommendation(message);
        break;
      case 'property_details':
        this.handlePropertyDetails(message);
        break;
      case 'error':
        this.handleError(message);
        break;
      case 'pong':
        // Handle ping/pong for connection health
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }

    // Call the general message handler
    this.onMessage && this.onMessage(message);
  }

  handleWelcome(message) {
    console.log('Welcome message:', message.data.message);
  }

  handleQuestion(message) {
    console.log('New question:', message.data);
    // The frontend should display this question to the user
  }

  handleRecommendation(message) {
    console.log('Property recommendations:', message.data);
    // Display property recommendations to the user
  }

  handlePropertyDetails(message) {
    console.log('Property details:', message.data);
    // Display detailed property information
  }

  handleError(message) {
    console.error('Chat error:', message.error);
    // Display error to user
  }

  sendUserResponse(questionId, answer, answerData = null) {
    const message = {
      type: 'user_response',
      session_id: this.sessionId,
      question_id: questionId,
      answer: answer,
      answer_data: answerData,
      timestamp: new Date().toISOString()
    };

    this.sendMessage(message);
  }

  requestPropertyDetails(propertyId) {
    const message = {
      type: 'request_details',
      property_id: propertyId
    };

    this.sendMessage(message);
  }

  restartSession() {
    const message = {
      type: 'restart_session'
    };

    this.sendMessage(message);
  }

  sendPing() {
    const message = {
      type: 'ping'
    };

    this.sendMessage(message);
  }

  sendMessage(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  attemptReconnect() {
    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay);

    // Exponential backoff
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
    }
  }
}

/**
 * Chat API Client for REST operations
 */
class ChatAPIClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async createSession() {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to create session: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  }

  async getUserSessions(limit = 10) {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions?limit=${limit}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get sessions: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  }

  async getSession(sessionId) {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions/${sessionId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get session: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  }

  async getChatHistory(sessionId) {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions/${sessionId}/history`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get chat history: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  }

  async deleteSession(sessionId) {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions/${sessionId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to delete session: ${response.statusText}`);
    }

    return true;
  }

  async restartSession(sessionId) {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/sessions/${sessionId}/restart`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to restart session: ${response.statusText}`);
    }

    return true;
  }

  async getConnectionStats() {
    const response = await fetch(`${this.baseUrl}/api/v1/chat/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get stats: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data;
  }
}

/**
 * Usage Example
 */
async function initializeChatSystem() {
  const baseUrl = 'http://localhost:8080';
  const token = 'your-jwt-token-here';

  // Create API client
  const apiClient = new ChatAPIClient(baseUrl, token);

  try {
    // Create a new chat session
    const session = await apiClient.createSession();
    console.log('Created session:', session);

    // Create WebSocket connection
    const chatWS = new ChatWebSocket(
      session.session_id,
      token,
      (message) => {
        // Handle incoming messages
        console.log('Message received:', message);
        
        if (message.type === 'question') {
          // Display question to user and collect response
          handleQuestion(message.data, chatWS);
        } else if (message.type === 'recommendation') {
          // Display property recommendations
          displayRecommendations(message.data.properties);
        }
      },
      (error) => {
        console.error('WebSocket error:', error);
      },
      (event) => {
        console.log('WebSocket closed:', event);
      }
    );

    // Connect to WebSocket
    chatWS.connect();

    // Example: Send a user response
    setTimeout(() => {
      chatWS.sendUserResponse('budget_range', '1000000-5000000', {
        min: 1000000,
        max: 5000000
      });
    }, 2000);

  } catch (error) {
    console.error('Failed to initialize chat system:', error);
  }
}

function handleQuestion(questionData, chatWS) {
  // This would typically be handled by your React component
  console.log('Question:', questionData.text);
  console.log('Type:', questionData.question_type);
  console.log('Options:', questionData.options);

  // Example auto-response for testing
  if (questionData.question_id === 'property_type') {
    setTimeout(() => {
      chatWS.sendUserResponse(questionData.question_id, 'Apartment');
    }, 1000);
  }
}

function displayRecommendations(properties) {
  console.log('Displaying recommendations:');
  properties.forEach((property, index) => {
    console.log(`${index + 1}. ${property.title} - ₹${property.total_price}`);
    console.log(`   Score: ${property.score}`);
    console.log(`   Reasons: ${property.match_reasons.join(', ')}`);
  });
}

// Export for use in React/other frameworks
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ChatWebSocket, ChatAPIClient };
}
