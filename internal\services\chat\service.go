package chat

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
	"realestate-platform/internal/services/websocket"
)

// Service provides chat functionality
type Service struct {
	db                   *mongodb.MongoDBClient
	redisClient          *redis.Client
	logger               *zap.Logger
	sessionManager       *SessionManager
	questionEngine       *QuestionEngine
	recommendationEngine *RecommendationEngine
	hub                  *websocket.Hub
}

// NewService creates a new chat service
func NewService(db *mongodb.MongoDBClient, redisClient *redis.Client, logger *zap.Logger) *Service {
	sessionManager := NewSessionManager(db, redisClient, logger)
	questionEngine := NewQuestionEngine(db, logger)
	recommendationEngine := NewRecommendationEngine(db, logger)
	hub := websocket.NewHub(redisClient, logger)

	return &Service{
		db:                   db,
		redisClient:          redisClient,
		logger:               logger,
		sessionManager:       sessionManager,
		questionEngine:       questionEngine,
		recommendationEngine: recommendationEngine,
		hub:                  hub,
	}
}

// GetHub returns the WebSocket hub
func (s *Service) GetHub() *websocket.Hub {
	return s.hub
}

// CreateSession creates a new chat session
func (s *Service) CreateSession(ctx context.Context, userID primitive.ObjectID) (*models.ChatSessionResponse, error) {
	session, err := s.sessionManager.CreateSession(ctx, userID)
	if err != nil {
		return nil, err
	}

	response := &models.ChatSessionResponse{
		ID:        session.ID,
		SessionID: session.SessionID,
		Status:    session.Status,
		CreatedAt: session.CreatedAt,
		ExpiresAt: session.ExpiresAt,
	}

	return response, nil
}

// GetUserSessions retrieves user's chat sessions
func (s *Service) GetUserSessions(ctx context.Context, userID primitive.ObjectID, limit int) ([]models.ChatSessionResponse, error) {
	sessions, err := s.sessionManager.GetUserSessions(ctx, userID, limit)
	if err != nil {
		return nil, err
	}

	var responses []models.ChatSessionResponse
	for _, session := range sessions {
		responses = append(responses, models.ChatSessionResponse{
			ID:        session.ID,
			SessionID: session.SessionID,
			Status:    session.Status,
			CreatedAt: session.CreatedAt,
			ExpiresAt: session.ExpiresAt,
		})
	}

	return responses, nil
}

// GetSession retrieves a specific session
func (s *Service) GetSession(ctx context.Context, sessionID string) (*models.ChatSessionResponse, error) {
	session, err := s.sessionManager.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	response := &models.ChatSessionResponse{
		ID:        session.ID,
		SessionID: session.SessionID,
		Status:    session.Status,
		CreatedAt: session.CreatedAt,
		ExpiresAt: session.ExpiresAt,
	}

	return response, nil
}

// DeleteSession deletes a chat session
func (s *Service) DeleteSession(ctx context.Context, sessionID string) error {
	return s.sessionManager.DeleteSession(ctx, sessionID)
}

// GetChatHistory retrieves chat history for a session
func (s *Service) GetChatHistory(ctx context.Context, sessionID string) (*models.ChatHistoryResponse, error) {
	session, err := s.sessionManager.GetSession(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Get messages
	messages, err := s.getChatMessages(ctx, session.ID)
	if err != nil {
		return nil, err
	}

	// Get responses
	responses, err := s.sessionManager.GetSessionResponses(ctx, session.ID)
	if err != nil {
		return nil, err
	}

	response := &models.ChatHistoryResponse{
		Session: models.ChatSessionResponse{
			ID:        session.ID,
			SessionID: session.SessionID,
			Status:    session.Status,
			CreatedAt: session.CreatedAt,
			ExpiresAt: session.ExpiresAt,
		},
		Messages:  messages,
		Responses: responses,
	}

	return response, nil
}

// RestartSession restarts a chat session
func (s *Service) RestartSession(ctx context.Context, sessionID string) error {
	// Update session status
	err := s.sessionManager.UpdateSessionStatus(ctx, sessionID, models.ChatSessionStatusActive)
	if err != nil {
		return err
	}

	// Send first question via WebSocket
	return s.sendFirstQuestion(ctx, sessionID)
}

// InitializeQuestions initializes default questions
func (s *Service) InitializeQuestions(ctx context.Context) error {
	return s.questionEngine.InitializeDefaultQuestions(ctx)
}

// CleanupExpiredSessions cleans up expired sessions
func (s *Service) CleanupExpiredSessions(ctx context.Context) error {
	return s.sessionManager.CleanupExpiredSessions(ctx)
}

// HandleUserResponse processes a user's response to a question
func (s *Service) HandleUserResponse(ctx context.Context, client *websocket.Client, message models.UserResponseMessage) error {
	s.logger.Info("Handling user response",
		zap.String("session_id", message.SessionID),
		zap.String("question_id", message.QuestionID),
		zap.String("answer", message.Answer),
	)

	// Get session
	session, err := s.sessionManager.GetSession(ctx, message.SessionID)
	if err != nil {
		return err
	}

	// Validate response
	validationResult, err := s.questionEngine.ValidateResponse(ctx, message.QuestionID, message.Answer, message.AnswerData)
	if err != nil {
		return err
	}

	if !validationResult.IsValid {
		// Send error message
		errorMsg := models.WebSocketMessage{
			Type:      models.MessageTypeError,
			SessionID: message.SessionID,
			Error:     validationResult.ErrorMessage,
			Timestamp: time.Now(),
		}
		return client.SendMessage(errorMsg)
	}

	// Store response
	userResponse := &models.UserResponse{
		SessionID:   session.ID,
		QuestionID:  message.QuestionID,
		Answer:      message.Answer,
		AnswerData:  message.AnswerData,
		RespondedAt: time.Now(),
	}

	if err := s.sessionManager.StoreUserResponse(ctx, userResponse); err != nil {
		return err
	}

	// Store chat message
	if err := s.storeChatMessage(ctx, session.ID, models.MessageTypeResponse, message.Answer, message.AnswerData, false); err != nil {
		s.logger.Warn("Failed to store chat message", zap.Error(err))
	}

	// Check if conversation is complete
	isComplete, err := s.questionEngine.IsConversationComplete(ctx, session.ID)
	if err != nil {
		return err
	}

	if isComplete {
		// Generate recommendations
		return s.generateAndSendRecommendations(ctx, client, session.ID)
	}

	// Get next question
	nextQuestion, err := s.questionEngine.GetNextQuestion(ctx, session.ID, message.QuestionID)
	if err != nil {
		if err == ErrNoMoreQuestions {
			// Generate recommendations
			return s.generateAndSendRecommendations(ctx, client, session.ID)
		}
		return err
	}

	// Send next question
	return s.sendQuestion(ctx, client, nextQuestion)
}

// HandleRequestDetails handles property details request
func (s *Service) HandleRequestDetails(ctx context.Context, client *websocket.Client, propertyID string) error {
	// Get property details
	property, err := s.getPropertyDetails(ctx, propertyID)
	if err != nil {
		return err
	}

	// Send property details
	detailsMsg := models.WebSocketMessage{
		Type:      "property_details",
		SessionID: client.SessionID,
		Data: map[string]interface{}{
			"property": property,
		},
		Timestamp: time.Now(),
	}

	return client.SendMessage(detailsMsg)
}

// HandleSessionRestart handles session restart request
func (s *Service) HandleSessionRestart(ctx context.Context, client *websocket.Client) error {
	return s.RestartSession(ctx, client.SessionID)
}

// sendFirstQuestion sends the first question to a client
func (s *Service) sendFirstQuestion(ctx context.Context, sessionID string) error {
	firstQuestion, err := s.questionEngine.GetFirstQuestion(ctx)
	if err != nil {
		return err
	}

	questionMsg := models.WebSocketMessage{
		Type:      models.MessageTypeQuestion,
		SessionID: sessionID,
		Data: map[string]interface{}{
			"question_id":   firstQuestion.QuestionID,
			"text":          firstQuestion.Text,
			"question_type": firstQuestion.QuestionType,
			"options":       firstQuestion.Options,
			"category":      firstQuestion.Category,
			"is_required":   firstQuestion.IsRequired,
		},
		Timestamp: time.Now(),
	}

	return s.hub.SendToSession(sessionID, questionMsg)
}

// sendQuestion sends a question to a client
func (s *Service) sendQuestion(ctx context.Context, client *websocket.Client, question *models.Question) error {
	questionMsg := models.WebSocketMessage{
		Type:      models.MessageTypeQuestion,
		SessionID: client.SessionID,
		Data: map[string]interface{}{
			"question_id":   question.QuestionID,
			"text":          question.Text,
			"question_type": question.QuestionType,
			"options":       question.Options,
			"category":      question.Category,
			"is_required":   question.IsRequired,
		},
		Timestamp: time.Now(),
	}

	// Store chat message
	session, _ := s.sessionManager.GetSession(ctx, client.SessionID)
	if session != nil {
		s.storeChatMessage(ctx, session.ID, models.MessageTypeQuestion, question.Text, map[string]interface{}{
			"question_id": question.QuestionID,
			"options":     question.Options,
		}, true)
	}

	return client.SendMessage(questionMsg)
}

// generateAndSendRecommendations generates and sends property recommendations
func (s *Service) generateAndSendRecommendations(ctx context.Context, client *websocket.Client, sessionID primitive.ObjectID) error {
	// Generate recommendations
	recommendations, err := s.recommendationEngine.GenerateRecommendations(ctx, sessionID, 10)
	if err != nil {
		return err
	}

	// Update session status
	s.sessionManager.UpdateSessionStatus(ctx, client.SessionID, models.ChatSessionStatusCompleted)

	// Send recommendations
	recommendationMsg := models.WebSocketMessage{
		Type:      models.MessageTypeRecommendation,
		SessionID: client.SessionID,
		Data: map[string]interface{}{
			"properties":       recommendations,
			"total_matches":    len(recommendations),
			"session_complete": true,
		},
		Timestamp: time.Now(),
	}

	// Store chat message
	s.storeChatMessage(ctx, sessionID, models.MessageTypeRecommendation, "Property recommendations generated", map[string]interface{}{
		"count": len(recommendations),
	}, true)

	return client.SendMessage(recommendationMsg)
}

// Helper methods

func (s *Service) getChatMessages(ctx context.Context, sessionID primitive.ObjectID) ([]models.ChatMessage, error) {
	collection := s.db.GetCollection("chat_messages")

	filter := bson.M{"session_id": sessionID}
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var messages []models.ChatMessage
	if err := cursor.All(ctx, &messages); err != nil {
		return nil, err
	}

	return messages, nil
}

func (s *Service) storeChatMessage(ctx context.Context, sessionID primitive.ObjectID, messageType, content string, data map[string]interface{}, isSystem bool) error {
	message := &models.ChatMessage{
		SessionID:   sessionID,
		MessageType: messageType,
		Content:     content,
		Data:        data,
		Timestamp:   time.Now(),
		IsSystem:    isSystem,
	}

	collection := s.db.GetCollection("chat_messages")
	_, err := collection.InsertOne(ctx, message)
	return err
}

func (s *Service) getPropertyDetails(ctx context.Context, propertyID string) (*models.Property, error) {
	collection := s.db.GetCollection("properties")

	objID, err := primitive.ObjectIDFromHex(propertyID)
	if err != nil {
		return nil, ErrPropertyNotFound
	}

	var property models.Property
	err = collection.FindOne(ctx, bson.M{"_id": objID}).Decode(&property)
	if err != nil {
		return nil, ErrPropertyNotFound
	}

	return &property, nil
}
