package websocket

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow connections from any origin for now
		// In production, you should validate the origin
		return true
	},
}

// Client is a middleman between the websocket connection and the hub
type Client struct {
	// The websocket connection
	conn *websocket.Conn

	// Buffered channel of outbound messages
	send chan []byte

	// The hub
	hub *Hub

	// User ID
	UserID primitive.ObjectID

	// Session ID
	SessionID string

	// Logger
	logger *zap.Logger

	// Message handler
	messageHandler MessageHandler
}

// MessageHandler interface for handling different types of messages
type MessageHandler interface {
	HandleUserResponse(client *Client, message models.UserResponseMessage) error
	HandleRequestDetails(client *Client, propertyID string) error
	HandleSessionRestart(client *Client) error
}

// NewClient creates a new WebSocket client
func NewClient(conn *websocket.Conn, hub *Hub, userID primitive.ObjectID, sessionID string, logger *zap.Logger, messageHandler MessageHandler) *Client {
	return &Client{
		conn:           conn,
		send:           make(chan []byte, 256),
		hub:            hub,
		UserID:         userID,
		SessionID:      sessionID,
		logger:         logger,
		messageHandler: messageHandler,
	}
}

// readPump pumps messages from the websocket connection to the hub
//
// The application runs readPump in a per-connection goroutine. The application
// ensures that there is at most one reader on a connection by executing all
// reads from this goroutine.
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.logger.Error("WebSocket error", zap.Error(err))
			}
			break
		}

		// Handle the incoming message
		c.handleMessage(message)
	}
}

// writePump pumps messages from the hub to the websocket connection
//
// A goroutine running writePump is started for each connection. The
// application ensures that there is at most one writer to a connection by
// executing all writes from this goroutine.
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued chat messages to the current websocket message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write(newline)
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processes incoming WebSocket messages
func (c *Client) handleMessage(message []byte) {
	c.logger.Debug("Received message",
		zap.String("user_id", c.UserID.Hex()),
		zap.String("session_id", c.SessionID),
		zap.String("message", string(message)),
	)

	// Parse the message to determine its type
	var baseMessage struct {
		Type string `json:"type"`
	}

	if err := json.Unmarshal(message, &baseMessage); err != nil {
		c.logger.Error("Failed to parse message", zap.Error(err))
		c.sendErrorMessage("Invalid message format")
		return
	}

	// Handle different message types
	switch baseMessage.Type {
	case "user_response":
		var userResponse models.UserResponseMessage
		if err := json.Unmarshal(message, &userResponse); err != nil {
			c.logger.Error("Failed to parse user response", zap.Error(err))
			c.sendErrorMessage("Invalid user response format")
			return
		}
		
		if err := c.messageHandler.HandleUserResponse(c, userResponse); err != nil {
			c.logger.Error("Failed to handle user response", zap.Error(err))
			c.sendErrorMessage("Failed to process your response")
		}

	case "request_details":
		var detailsRequest struct {
			Type       string `json:"type"`
			PropertyID string `json:"property_id"`
		}
		if err := json.Unmarshal(message, &detailsRequest); err != nil {
			c.logger.Error("Failed to parse details request", zap.Error(err))
			c.sendErrorMessage("Invalid details request format")
			return
		}

		if err := c.messageHandler.HandleRequestDetails(c, detailsRequest.PropertyID); err != nil {
			c.logger.Error("Failed to handle details request", zap.Error(err))
			c.sendErrorMessage("Failed to get property details")
		}

	case "restart_session":
		if err := c.messageHandler.HandleSessionRestart(c); err != nil {
			c.logger.Error("Failed to restart session", zap.Error(err))
			c.sendErrorMessage("Failed to restart session")
		}

	case "ping":
		c.sendPongMessage()

	default:
		c.logger.Warn("Unknown message type", zap.String("type", baseMessage.Type))
		c.sendErrorMessage("Unknown message type")
	}
}

// sendErrorMessage sends an error message to the client
func (c *Client) sendErrorMessage(errorMsg string) {
	errorMessage := models.WebSocketMessage{
		Type:      models.MessageTypeError,
		SessionID: c.SessionID,
		Error:     errorMsg,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(errorMessage)
	if err != nil {
		c.logger.Error("Failed to marshal error message", zap.Error(err))
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		c.logger.Warn("Failed to send error message")
	}
}

// sendPongMessage sends a pong response to ping
func (c *Client) sendPongMessage() {
	pongMessage := models.WebSocketMessage{
		Type:      "pong",
		SessionID: c.SessionID,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(pongMessage)
	if err != nil {
		c.logger.Error("Failed to marshal pong message", zap.Error(err))
		return
	}

	select {
	case c.send <- messageBytes:
	default:
		c.logger.Warn("Failed to send pong message")
	}
}

// SendMessage sends a message to the client
func (c *Client) SendMessage(message models.WebSocketMessage) error {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		c.logger.Error("Failed to marshal message", zap.Error(err))
		return err
	}

	select {
	case c.send <- messageBytes:
		return nil
	default:
		c.logger.Warn("Failed to send message to client")
		return ErrClientNotReachable
	}
}

// Start starts the client's read and write pumps
func (c *Client) Start() {
	// Register the client with the hub
	c.hub.register <- c

	// Start the write pump in a goroutine
	go c.writePump()

	// Start the read pump in the current goroutine
	// This will block until the connection is closed
	c.readPump()
}
