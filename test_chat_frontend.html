<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Estate Chat System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .step {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .success {
            color: #28a745;
            font-weight: bold;
        }

        .error {
            color: #dc3545;
            font-weight: bold;
        }

        .info {
            color: #17a2b8;
        }

        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .chat-container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            background-color: #fff;
        }

        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }

        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }

        .system-message {
            background-color: #e9ecef;
            color: #333;
        }

        .question {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .options {
            margin: 10px 0;
        }

        .option-button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
        }

        .option-button:hover {
            background-color: #e9ecef;
        }

        .recommendations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .property-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
        }

        .property-score {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
    </style>
</head>

<body>
    <h1>🏠 Real Estate Chat System Test</h1>

    <div class="container">
        <h2>Step 1: Authentication</h2>
        <div class="step">
            <h3>1.1 Register User</h3>
            <button onclick="registerUser()">Register Test User</button>
            <div id="registerResult" class="log"></div>
        </div>

        <div class="step">
            <h3>1.2 Login User</h3>
            <button onclick="loginUser()">Login Test User</button>
            <div id="loginResult" class="log"></div>
        </div>
    </div>

    <div class="container">
        <h2>Step 2: Chat API Testing</h2>
        <div class="step">
            <h3>2.1 Initialize Questions</h3>
            <button onclick="initializeQuestions()" id="initBtn" disabled>Initialize Default Questions</button>
            <div id="initResult" class="log"></div>
        </div>

        <div class="step">
            <h3>2.2 Create Chat Session</h3>
            <button onclick="createChatSession()" id="sessionBtn" disabled>Create Chat Session</button>
            <div id="sessionResult" class="log"></div>
        </div>

        <div class="step">
            <h3>2.3 Get Connection Stats</h3>
            <button onclick="getConnectionStats()" id="statsBtn" disabled>Get Connection Stats</button>
            <div id="statsResult" class="log"></div>
        </div>
    </div>

    <div class="container">
        <h2>Step 3: WebSocket Chat Testing</h2>
        <div class="step">
            <h3>3.1 Connect to WebSocket</h3>
            <button onclick="connectWebSocket()" id="wsBtn" disabled>Connect to Chat</button>
            <button onclick="disconnectWebSocket()" id="disconnectBtn" disabled>Disconnect</button>
            <div id="wsStatus" class="log"></div>
        </div>

        <div class="step">
            <h3>3.2 Chat Interface</h3>
            <div id="chatContainer" class="chat-container" style="display: none;">
                <div id="messages"></div>
                <div id="currentQuestion"></div>
                <div id="recommendations"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        let jwtToken = '';
        let sessionId = '';
        let chatWebSocket = null;

        // Step 1: Authentication
        async function registerUser() {
            const userData = {
                first_name: "John",
                last_name: "Doe",
                email: "<EMAIL>",
                password: "Password123!",
                phone_number: "+91 9876543210"
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('registerResult').innerHTML =
                        `<span class="success">✅ User registered successfully!</span>\nUser ID: ${result.data.user.id}`;
                } else {
                    if (response.status === 409) {
                        document.getElementById('registerResult').innerHTML =
                            `<span class="info">ℹ️ User already exists, you can proceed to login</span>`;
                    } else {
                        throw new Error(result.error || 'Registration failed');
                    }
                }
            } catch (error) {
                document.getElementById('registerResult').innerHTML =
                    `<span class="error">❌ Registration failed: ${error.message}</span>`;
            }
        }

        async function loginUser() {
            const loginData = {
                email: "<EMAIL>",
                password: "password123"
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                const result = await response.json();

                if (response.ok) {
                    jwtToken = result.data.token;
                    document.getElementById('loginResult').innerHTML =
                        `<span class="success">✅ Login successful!</span>\nJWT Token: ${jwtToken.substring(0, 50)}...`;

                    // Enable next step buttons
                    document.getElementById('initBtn').disabled = false;
                    document.getElementById('sessionBtn').disabled = false;
                    document.getElementById('statsBtn').disabled = false;
                } else {
                    throw new Error(result.error || 'Login failed');
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML =
                    `<span class="error">❌ Login failed: ${error.message}</span>`;
            }
        }

        // Step 2: Chat API Testing
        async function initializeQuestions() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/chat/questions/initialize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${jwtToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('initResult').innerHTML =
                        `<span class="success">✅ Questions initialized successfully!</span>`;
                } else {
                    throw new Error(result.error || 'Initialization failed');
                }
            } catch (error) {
                document.getElementById('initResult').innerHTML =
                    `<span class="error">❌ Initialization failed: ${error.message}</span>`;
            }
        }

        async function createChatSession() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/chat/sessions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${jwtToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    sessionId = result.data.session_id;
                    document.getElementById('sessionResult').innerHTML =
                        `<span class="success">✅ Chat session created!</span>\nSession ID: ${sessionId}\nStatus: ${result.data.status}`;

                    // Enable WebSocket button
                    document.getElementById('wsBtn').disabled = false;
                } else {
                    throw new Error(result.error || 'Session creation failed');
                }
            } catch (error) {
                document.getElementById('sessionResult').innerHTML =
                    `<span class="error">❌ Session creation failed: ${error.message}</span>`;
            }
        }

        async function getConnectionStats() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/chat/stats`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${jwtToken}`
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    document.getElementById('statsResult').innerHTML =
                        `<span class="success">✅ Connection stats retrieved!</span>\nTotal Connections: ${result.data.total_connections}\nMax Connections: ${result.data.max_connections}`;
                } else {
                    throw new Error(result.error || 'Stats retrieval failed');
                }
            } catch (error) {
                document.getElementById('statsResult').innerHTML =
                    `<span class="error">❌ Stats retrieval failed: ${error.message}</span>`;
            }
        }

        // Step 3: WebSocket Testing
        function connectWebSocket() {
            if (!sessionId) {
                alert('Please create a chat session first!');
                return;
            }

            const wsUrl = `ws://localhost:8080/api/v1/chat/ws/${sessionId}?token=${jwtToken}`;

            try {
                chatWebSocket = new WebSocket(wsUrl);

                chatWebSocket.onopen = function (event) {
                    document.getElementById('wsStatus').innerHTML =
                        `<span class="success">✅ WebSocket connected successfully!</span>\nURL: ${wsUrl}`;
                    document.getElementById('chatContainer').style.display = 'block';
                    document.getElementById('wsBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                };

                chatWebSocket.onmessage = function (event) {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                };

                chatWebSocket.onerror = function (error) {
                    document.getElementById('wsStatus').innerHTML +=
                        `\n<span class="error">❌ WebSocket error: ${error}</span>`;
                };

                chatWebSocket.onclose = function (event) {
                    document.getElementById('wsStatus').innerHTML +=
                        `\n<span class="info">ℹ️ WebSocket closed: ${event.code} - ${event.reason}</span>`;
                    document.getElementById('wsBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                };

            } catch (error) {
                document.getElementById('wsStatus').innerHTML =
                    `<span class="error">❌ WebSocket connection failed: ${error.message}</span>`;
            }
        }

        function disconnectWebSocket() {
            if (chatWebSocket) {
                chatWebSocket.close();
                chatWebSocket = null;
            }
        }

        function handleWebSocketMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const questionDiv = document.getElementById('currentQuestion');
            const recommendationsDiv = document.getElementById('recommendations');

            // Add message to chat
            const messageElement = document.createElement('div');
            messageElement.className = 'message system-message';
            messageElement.innerHTML = `<strong>${message.type}:</strong> ${JSON.stringify(message.data || message.error || 'Connected', null, 2)}`;
            messagesDiv.appendChild(messageElement);

            switch (message.type) {
                case 'welcome':
                    // Welcome message received
                    break;

                case 'question':
                    displayQuestion(message.data);
                    break;

                case 'recommendation':
                    displayRecommendations(message.data);
                    questionDiv.innerHTML = ''; // Clear current question
                    break;

                case 'error':
                    questionDiv.innerHTML = `<div class="error">Error: ${message.error}</div>`;
                    break;
            }

            // Scroll to bottom
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function displayQuestion(questionData) {
            const questionDiv = document.getElementById('currentQuestion');

            let optionsHtml = '';
            if (questionData.question_type === 'single_choice' && questionData.options) {
                optionsHtml = questionData.options.map(option =>
                    `<button class="option-button" onclick="sendResponse('${questionData.question_id}', '${option}')">${option}</button>`
                ).join('');
            } else if (questionData.question_type === 'boolean') {
                optionsHtml = `
                    <button class="option-button" onclick="sendResponse('${questionData.question_id}', 'yes')">Yes</button>
                    <button class="option-button" onclick="sendResponse('${questionData.question_id}', 'no')">No</button>
                `;
            } else if (questionData.question_type === 'range') {
                optionsHtml = `
                    <div>
                        <input type="number" id="minRange" placeholder="Minimum" style="margin: 5px; padding: 8px;">
                        <input type="number" id="maxRange" placeholder="Maximum" style="margin: 5px; padding: 8px;">
                        <button onclick="sendRangeResponse('${questionData.question_id}')">Submit Range</button>
                    </div>
                `;
            }

            questionDiv.innerHTML = `
                <div class="question">
                    <h4>${questionData.text} ${questionData.is_required ? '*' : ''}</h4>
                    <p><strong>Type:</strong> ${questionData.question_type}</p>
                    <p><strong>Category:</strong> ${questionData.category}</p>
                    <div class="options">${optionsHtml}</div>
                </div>
            `;
        }

        function sendResponse(questionId, answer) {
            const response = {
                type: 'user_response',
                session_id: sessionId,
                question_id: questionId,
                answer: answer,
                timestamp: new Date().toISOString()
            };

            chatWebSocket.send(JSON.stringify(response));

            // Add user message to chat
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = 'message user-message';
            messageElement.innerHTML = `<strong>You:</strong> ${answer}`;
            messagesDiv.appendChild(messageElement);
        }

        function sendRangeResponse(questionId) {
            const min = document.getElementById('minRange').value;
            const max = document.getElementById('maxRange').value;

            if (!min || !max || parseFloat(min) >= parseFloat(max)) {
                alert('Please enter valid range values');
                return;
            }

            const response = {
                type: 'user_response',
                session_id: sessionId,
                question_id: questionId,
                answer: `${min}-${max}`,
                answer_data: {
                    min: parseFloat(min),
                    max: parseFloat(max)
                },
                timestamp: new Date().toISOString()
            };

            chatWebSocket.send(JSON.stringify(response));

            // Add user message to chat
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            messageElement.className = 'message user-message';
            messageElement.innerHTML = `<strong>You:</strong> ${min} - ${max}`;
            messagesDiv.appendChild(messageElement);
        }

        function displayRecommendations(data) {
            const recommendationsDiv = document.getElementById('recommendations');

            if (data.properties && data.properties.length > 0) {
                const propertiesHtml = data.properties.map(property => `
                    <div class="property-card">
                        <h4>${property.title || 'Property'}</h4>
                        <div class="property-score">Score: ${Math.round(property.score * 100)}%</div>
                        <p><strong>Price:</strong> ₹${property.total_price?.toLocaleString()}</p>
                        <p><strong>Location:</strong> ${property.location?.area}, ${property.location?.city}</p>
                        <p><strong>Type:</strong> ${property.type}</p>
                        <p><strong>BHK:</strong> ${property.bhk}</p>
                        ${property.match_reasons ? `
                            <div>
                                <strong>Why this matches:</strong>
                                <ul>${property.match_reasons.map(reason => `<li>${reason}</li>`).join('')}</ul>
                            </div>
                        ` : ''}
                    </div>
                `).join('');

                recommendationsDiv.innerHTML = `
                    <h3>🏠 Property Recommendations (${data.total_matches} found)</h3>
                    <div class="recommendations">${propertiesHtml}</div>
                    <button onclick="restartChat()">Start New Search</button>
                `;
            } else {
                recommendationsDiv.innerHTML = `
                    <h3>No properties found matching your criteria</h3>
                    <button onclick="restartChat()">Start New Search</button>
                `;
            }
        }

        function restartChat() {
            // Clear chat interface
            document.getElementById('messages').innerHTML = '';
            document.getElementById('currentQuestion').innerHTML = '';
            document.getElementById('recommendations').innerHTML = '';

            // Send restart message
            const restartMessage = {
                type: 'restart_session'
            };
            chatWebSocket.send(JSON.stringify(restartMessage));
        }
    </script>
</body>

</html>