package chat

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/config"
	"realestate-platform/internal/services/websocket"
)

// WebSocketHandler handles WebSocket connections for chat
type WebSocketHandler struct {
	hub            *websocket.Hub
	messageHandler websocket.MessageHandler
	config         *config.Config
	logger         *zap.Logger
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(hub *websocket.Hub, messageHandler websocket.MessageHandler, config *config.Config, logger *zap.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		hub:            hub,
		messageHandler: messageHandler,
		config:         config,
		logger:         logger,
	}
}

// HandleWebSocket handles WebSocket connection upgrade
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		h.logger.Warn("Missing session ID in WebSocket request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "session_id is required"})
		return
	}

	// Authenticate the user via JWT token
	userID, err := h.authenticateWebSocket(c)
	if err != nil {
		h.logger.Warn("WebSocket authentication failed", zap.Error(err))
		c.JSON(http.StatusUnauthorized, gin.H{"error": "authentication failed"})
		return
	}

	// Check connection limits
	if h.hub.GetUserSessionsCount(userID.Hex()) >= websocket.MaxConnectionsPerUser {
		h.logger.Warn("Max connections per user reached",
			zap.String("user_id", userID.Hex()),
			zap.Int("current_connections", h.hub.GetUserSessionsCount(userID.Hex())),
		)
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "maximum connections per user reached"})
		return
	}

	if h.hub.GetActiveSessionsCount() >= websocket.MaxTotalConnections {
		h.logger.Warn("Max total connections reached",
			zap.Int("total_connections", h.hub.GetActiveSessionsCount()),
		)
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "service temporarily unavailable"})
		return
	}

	// Upgrade HTTP connection to WebSocket
	conn, err := websocket.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Error("Failed to upgrade connection to WebSocket", zap.Error(err))
		return
	}

	// Create new client
	client := websocket.NewClient(conn, h.hub, userID, sessionID, h.logger, h.messageHandler)

	h.logger.Info("WebSocket connection established",
		zap.String("user_id", userID.Hex()),
		zap.String("session_id", sessionID),
		zap.String("remote_addr", c.Request.RemoteAddr),
	)

	// Start the client (this will block until connection is closed)
	client.Start()
}

// authenticateWebSocket authenticates WebSocket connection using JWT token
func (h *WebSocketHandler) authenticateWebSocket(c *gin.Context) (primitive.ObjectID, error) {
	// Try to get token from query parameter first (for WebSocket connections)
	tokenString := c.Query("token")
	
	// If not in query, try Authorization header
	if tokenString == "" {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			return primitive.NilObjectID, websocket.ErrUnauthorized
		}

		// Extract token from "Bearer <token>" format
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			return primitive.NilObjectID, websocket.ErrUnauthorized
		}
		tokenString = parts[1]
	}

	if tokenString == "" {
		return primitive.NilObjectID, websocket.ErrUnauthorized
	}

	// Parse and validate JWT token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return []byte(h.config.JWT.SecretKey), nil
	})

	if err != nil {
		return primitive.NilObjectID, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// Extract user ID from claims
		userIDStr, ok := claims["user_id"].(string)
		if !ok {
			return primitive.NilObjectID, websocket.ErrUnauthorized
		}

		userID, err := primitive.ObjectIDFromHex(userIDStr)
		if err != nil {
			return primitive.NilObjectID, websocket.ErrUnauthorized
		}

		return userID, nil
	}

	return primitive.NilObjectID, websocket.ErrUnauthorized
}

// GetConnectionStats returns WebSocket connection statistics
func (h *WebSocketHandler) GetConnectionStats(c *gin.Context) {
	stats := gin.H{
		"total_connections": h.hub.GetActiveSessionsCount(),
		"max_connections":   websocket.MaxTotalConnections,
	}

	// If user is authenticated, include their connection count
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(primitive.ObjectID); ok {
			stats["user_connections"] = h.hub.GetUserSessionsCount(uid.Hex())
			stats["max_user_connections"] = websocket.MaxConnectionsPerUser
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
