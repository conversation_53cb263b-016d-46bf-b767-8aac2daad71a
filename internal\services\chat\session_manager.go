package chat

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

// SessionManager manages chat sessions with Redis caching and MongoDB persistence
type SessionManager struct {
	db          *mongodb.MongoDBClient
	redisClient *redis.Client
	logger      *zap.Logger
}

// NewSessionManager creates a new session manager
func NewSessionManager(db *mongodb.MongoDBClient, redisClient *redis.Client, logger *zap.Logger) *SessionManager {
	return &SessionManager{
		db:          db,
		redisClient: redisClient,
		logger:      logger,
	}
}

// CreateSession creates a new chat session
func (sm *SessionManager) CreateSession(ctx context.Context, userID primitive.ObjectID) (*models.ChatSession, error) {
	// Generate unique session ID
	sessionID := uuid.New().String()

	// Create session object
	session := &models.ChatSession{
		UserID:    userID,
		SessionID: sessionID,
		Status:    models.ChatSessionStatusActive,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		ExpiresAt: time.Now().Add(time.Hour), // 1 hour expiry
		Metadata:  make(map[string]interface{}),
	}

	// Store in MongoDB
	collection := sm.db.GetCollection("chat_sessions")
	result, err := collection.InsertOne(ctx, session)
	if err != nil {
		sm.logger.Error("Failed to create session in MongoDB", zap.Error(err))
		return nil, err
	}

	session.ID = result.InsertedID.(primitive.ObjectID)

	// Cache in Redis
	if err := sm.cacheSession(ctx, session); err != nil {
		sm.logger.Warn("Failed to cache session in Redis", zap.Error(err))
		// Don't fail the request if Redis caching fails
	}

	sm.logger.Info("Chat session created",
		zap.String("user_id", userID.Hex()),
		zap.String("session_id", sessionID),
	)

	return session, nil
}

// GetSession retrieves a session by ID (tries Redis first, then MongoDB)
func (sm *SessionManager) GetSession(ctx context.Context, sessionID string) (*models.ChatSession, error) {
	// Try Redis first
	if session, err := sm.getSessionFromCache(ctx, sessionID); err == nil {
		return session, nil
	}

	// Fallback to MongoDB
	session, err := sm.getSessionFromDB(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Cache the session for future requests
	if err := sm.cacheSession(ctx, session); err != nil {
		sm.logger.Warn("Failed to cache session", zap.Error(err))
	}

	return session, nil
}

// GetUserSessions retrieves all sessions for a user
func (sm *SessionManager) GetUserSessions(ctx context.Context, userID primitive.ObjectID, limit int) ([]models.ChatSession, error) {
	collection := sm.db.GetCollection("chat_sessions")

	filter := bson.M{"user_id": userID}
	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetLimit(int64(limit))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		sm.logger.Error("Failed to get user sessions", zap.Error(err))
		return nil, err
	}
	defer cursor.Close(ctx)

	var sessions []models.ChatSession
	if err := cursor.All(ctx, &sessions); err != nil {
		sm.logger.Error("Failed to decode user sessions", zap.Error(err))
		return nil, err
	}

	return sessions, nil
}

// UpdateSessionStatus updates the status of a session
func (sm *SessionManager) UpdateSessionStatus(ctx context.Context, sessionID string, status string) error {
	// Update in MongoDB
	collection := sm.db.GetCollection("chat_sessions")
	filter := bson.M{"session_id": sessionID}
	update := bson.M{
		"$set": bson.M{
			"status":     status,
			"updated_at": time.Now(),
		},
	}

	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		sm.logger.Error("Failed to update session status in MongoDB", zap.Error(err))
		return err
	}

	if result.MatchedCount == 0 {
		return ErrSessionNotFound
	}

	// Update in Redis cache
	if err := sm.updateSessionStatusInCache(ctx, sessionID, status); err != nil {
		sm.logger.Warn("Failed to update session status in Redis", zap.Error(err))
	}

	sm.logger.Info("Session status updated",
		zap.String("session_id", sessionID),
		zap.String("status", status),
	)

	return nil
}

// DeleteSession deletes a session
func (sm *SessionManager) DeleteSession(ctx context.Context, sessionID string) error {
	// Delete from MongoDB
	collection := sm.db.GetCollection("chat_sessions")
	filter := bson.M{"session_id": sessionID}

	result, err := collection.DeleteOne(ctx, filter)
	if err != nil {
		sm.logger.Error("Failed to delete session from MongoDB", zap.Error(err))
		return err
	}

	if result.DeletedCount == 0 {
		return ErrSessionNotFound
	}

	// Delete from Redis cache
	cacheKey := fmt.Sprintf("chat:session:%s", sessionID)
	if err := sm.redisClient.Del(ctx, cacheKey).Err(); err != nil {
		sm.logger.Warn("Failed to delete session from Redis", zap.Error(err))
	}

	sm.logger.Info("Session deleted", zap.String("session_id", sessionID))
	return nil
}

// StoreUserResponse stores a user's response to a question
func (sm *SessionManager) StoreUserResponse(ctx context.Context, response *models.UserResponse) error {
	// Store in MongoDB
	collection := sm.db.GetCollection("user_responses")
	result, err := collection.InsertOne(ctx, response)
	if err != nil {
		sm.logger.Error("Failed to store user response", zap.Error(err))
		return err
	}

	response.ID = result.InsertedID.(primitive.ObjectID)

	// Cache recent responses in Redis
	if err := sm.cacheUserResponse(ctx, response); err != nil {
		sm.logger.Warn("Failed to cache user response", zap.Error(err))
	}

	return nil
}

// GetSessionResponses retrieves all responses for a session
func (sm *SessionManager) GetSessionResponses(ctx context.Context, sessionID primitive.ObjectID) ([]models.UserResponse, error) {
	collection := sm.db.GetCollection("user_responses")

	filter := bson.M{"session_id": sessionID}
	opts := options.Find().SetSort(bson.D{{Key: "responded_at", Value: 1}})

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		sm.logger.Error("Failed to get session responses", zap.Error(err))
		return nil, err
	}
	defer cursor.Close(ctx)

	var responses []models.UserResponse
	if err := cursor.All(ctx, &responses); err != nil {
		sm.logger.Error("Failed to decode session responses", zap.Error(err))
		return nil, err
	}

	return responses, nil
}

// CleanupExpiredSessions removes expired sessions
func (sm *SessionManager) CleanupExpiredSessions(ctx context.Context) error {
	collection := sm.db.GetCollection("chat_sessions")

	filter := bson.M{
		"expires_at": bson.M{"$lt": time.Now()},
		"status":     bson.M{"$ne": models.ChatSessionStatusCompleted},
	}

	update := bson.M{
		"$set": bson.M{
			"status":     models.ChatSessionStatusExpired,
			"updated_at": time.Now(),
		},
	}

	result, err := collection.UpdateMany(ctx, filter, update)
	if err != nil {
		sm.logger.Error("Failed to cleanup expired sessions", zap.Error(err))
		return err
	}

	if result.ModifiedCount > 0 {
		sm.logger.Info("Cleaned up expired sessions", zap.Int64("count", result.ModifiedCount))
	}

	return nil
}

// cacheSession stores session in Redis
func (sm *SessionManager) cacheSession(ctx context.Context, session *models.ChatSession) error {
	cacheKey := fmt.Sprintf("chat:session:%s", session.SessionID)

	sessionJSON, err := json.Marshal(session)
	if err != nil {
		return err
	}

	return sm.redisClient.Set(ctx, cacheKey, sessionJSON, time.Hour).Err()
}

// getSessionFromCache retrieves session from Redis
func (sm *SessionManager) getSessionFromCache(ctx context.Context, sessionID string) (*models.ChatSession, error) {
	cacheKey := fmt.Sprintf("chat:session:%s", sessionID)

	sessionJSON, err := sm.redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		return nil, err
	}

	var session models.ChatSession
	if err := json.Unmarshal([]byte(sessionJSON), &session); err != nil {
		return nil, err
	}

	return &session, nil
}

// getSessionFromDB retrieves session from MongoDB
func (sm *SessionManager) getSessionFromDB(ctx context.Context, sessionID string) (*models.ChatSession, error) {
	collection := sm.db.GetCollection("chat_sessions")

	var session models.ChatSession
	err := collection.FindOne(ctx, bson.M{"session_id": sessionID}).Decode(&session)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrSessionNotFound
		}
		return nil, err
	}

	return &session, nil
}

// updateSessionStatusInCache updates session status in Redis
func (sm *SessionManager) updateSessionStatusInCache(ctx context.Context, sessionID, status string) error {
	cacheKey := fmt.Sprintf("chat:session:%s", sessionID)

	// Get existing session
	sessionJSON, err := sm.redisClient.Get(ctx, cacheKey).Result()
	if err != nil {
		return err // Session not in cache, that's okay
	}

	var session models.ChatSession
	if err := json.Unmarshal([]byte(sessionJSON), &session); err != nil {
		return err
	}

	// Update status
	session.Status = status
	session.UpdatedAt = time.Now()

	// Store back
	updatedJSON, err := json.Marshal(session)
	if err != nil {
		return err
	}

	return sm.redisClient.Set(ctx, cacheKey, updatedJSON, time.Hour).Err()
}

// cacheUserResponse caches user response in Redis
func (sm *SessionManager) cacheUserResponse(ctx context.Context, response *models.UserResponse) error {
	cacheKey := fmt.Sprintf("chat:response:%s:%s", response.SessionID.Hex(), response.QuestionID)

	responseJSON, err := json.Marshal(response)
	if err != nil {
		return err
	}

	return sm.redisClient.Set(ctx, cacheKey, responseJSON, time.Hour).Err()
}
