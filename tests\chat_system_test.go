package tests

import (
	"context"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/config"
	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
	"realestate-platform/internal/services/chat"
)

func TestChatSystemIntegration(t *testing.T) {
	// Skip if running in CI without database
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup test configuration
	cfg := &config.Config{
		MongoDB: config.MongoDBConfig{
			URI:      "mongodb://localhost:27018",
			Database: "realestate_test",
		},
		Redis: config.RedisConfig{
			Address:  "localhost:6379",
			Password: "",
			DB:       1, // Use different DB for tests
		},
	}

	// Setup logger
	logger, _ := zap.NewDevelopment()

	// Setup MongoDB
	mongoClient, err := mongodb.NewMongoDBClient(cfg.MongoDB)
	require.NoError(t, err)
	defer mongoClient.Disconnect(context.Background())

	// Setup Redis
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Address,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	defer redisClient.Close()

	// Test Redis connection
	ctx := context.Background()
	_, err = redisClient.Ping(ctx).Result()
	require.NoError(t, err)

	// Create chat service
	chatService := chat.NewService(mongoClient, redisClient, logger)

	// Initialize questions
	err = chatService.InitializeQuestions(ctx)
	require.NoError(t, err)

	t.Run("CreateChatSession", func(t *testing.T) {
		userID := primitive.NewObjectID()

		session, err := chatService.CreateSession(ctx, userID)
		require.NoError(t, err)
		assert.NotEmpty(t, session.SessionID)
		assert.Equal(t, models.ChatSessionStatusActive, session.Status)
		assert.NotEmpty(t, session.ID) // Just check that ID is not empty
	})

	t.Run("GetUserSessions", func(t *testing.T) {
		userID := primitive.NewObjectID()

		// Create multiple sessions
		session1, err := chatService.CreateSession(ctx, userID)
		require.NoError(t, err)

		session2, err := chatService.CreateSession(ctx, userID)
		require.NoError(t, err)

		// Get user sessions
		sessions, err := chatService.GetUserSessions(ctx, userID, 10)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(sessions), 2)

		// Verify sessions are returned in correct order (newest first)
		assert.True(t, sessions[0].CreatedAt.After(sessions[1].CreatedAt) || sessions[0].CreatedAt.Equal(sessions[1].CreatedAt))

		// Clean up
		chatService.DeleteSession(ctx, session1.SessionID)
		chatService.DeleteSession(ctx, session2.SessionID)
	})

	t.Run("SessionManagement", func(t *testing.T) {
		userID := primitive.NewObjectID()

		// Create session
		session, err := chatService.CreateSession(ctx, userID)
		require.NoError(t, err)

		// Get session
		retrievedSession, err := chatService.GetSession(ctx, session.SessionID)
		require.NoError(t, err)
		assert.Equal(t, session.SessionID, retrievedSession.SessionID)

		// Delete session
		err = chatService.DeleteSession(ctx, session.SessionID)
		require.NoError(t, err)

		// Verify session is deleted
		_, err = chatService.GetSession(ctx, session.SessionID)
		assert.Error(t, err)
	})

	t.Run("QuestionEngine", func(t *testing.T) {
		// Test question engine functionality
		questionEngine := chat.NewQuestionEngine(mongoClient, logger)

		// Get first question
		firstQuestion, err := questionEngine.GetFirstQuestion(ctx)
		require.NoError(t, err)
		assert.NotEmpty(t, firstQuestion.QuestionID)
		assert.Equal(t, 1, firstQuestion.Order)
		assert.True(t, firstQuestion.IsActive)
	})

	t.Run("RecommendationEngine", func(t *testing.T) {
		// Create a test session with responses
		userID := primitive.NewObjectID()
		session, err := chatService.CreateSession(ctx, userID)
		require.NoError(t, err)
		defer chatService.DeleteSession(ctx, session.SessionID)

		// Get session from database to get the ObjectID
		sessionData, err := chatService.GetSession(ctx, session.SessionID)
		require.NoError(t, err)

		// Create test user responses
		sessionManager := chat.NewSessionManager(mongoClient, redisClient, logger)

		responses := []models.UserResponse{
			{
				SessionID:   sessionData.ID,
				QuestionID:  "budget_range",
				Answer:      "1000000-5000000",
				AnswerData:  map[string]interface{}{"min": 1000000.0, "max": 5000000.0},
				RespondedAt: time.Now(),
			},
			{
				SessionID:   sessionData.ID,
				QuestionID:  "property_type",
				Answer:      "apartment",
				RespondedAt: time.Now(),
			},
			{
				SessionID:   sessionData.ID,
				QuestionID:  "bhk_preference",
				Answer:      "2 BHK",
				RespondedAt: time.Now(),
			},
		}

		for _, response := range responses {
			err := sessionManager.StoreUserResponse(ctx, &response)
			require.NoError(t, err)
		}

		// Test recommendation generation
		recommendationEngine := chat.NewRecommendationEngine(mongoClient, logger)
		recommendations, err := recommendationEngine.GenerateRecommendations(ctx, sessionData.ID, 5)

		// Note: This might return empty results if no properties match, which is okay for testing
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(recommendations), 0)

		// If recommendations exist, verify structure
		for _, rec := range recommendations {
			assert.NotEmpty(t, rec.Property.ID)
			assert.GreaterOrEqual(t, rec.Score, 0.0)
			assert.LessOrEqual(t, rec.Score, 1.0)
			assert.NotNil(t, rec.MatchingCriteria)
		}
	})

	t.Run("SessionCleanup", func(t *testing.T) {
		sessionManager := chat.NewSessionManager(mongoClient, redisClient, logger)

		// Test cleanup of expired sessions
		err := sessionManager.CleanupExpiredSessions(ctx)
		require.NoError(t, err)
	})
}

func TestQuestionValidation(t *testing.T) {
	// Setup
	cfg := &config.Config{
		MongoDB: config.MongoDBConfig{
			URI:      "mongodb://localhost:27018",
			Database: "realestate_test",
		},
	}

	logger, _ := zap.NewDevelopment()
	mongoClient, err := mongodb.NewMongoDBClient(cfg.MongoDB)
	require.NoError(t, err)
	defer mongoClient.Disconnect(context.Background())

	questionEngine := chat.NewQuestionEngine(mongoClient, logger)
	ctx := context.Background()

	// Initialize questions
	err = questionEngine.InitializeDefaultQuestions(ctx)
	require.NoError(t, err)

	t.Run("ValidateSingleChoice", func(t *testing.T) {
		response, err := questionEngine.ValidateResponse(ctx, "property_type", "Apartment", nil)
		require.NoError(t, err)
		assert.True(t, response.IsValid)
		assert.Empty(t, response.ErrorMessage)
	})

	t.Run("ValidateInvalidChoice", func(t *testing.T) {
		response, err := questionEngine.ValidateResponse(ctx, "property_type", "InvalidType", nil)
		require.NoError(t, err)
		assert.False(t, response.IsValid)
		assert.NotEmpty(t, response.ErrorMessage)
	})

	t.Run("ValidateRange", func(t *testing.T) {
		answerData := map[string]interface{}{
			"min": 1000000.0,
			"max": 5000000.0,
		}
		response, err := questionEngine.ValidateResponse(ctx, "budget_range", "", answerData)
		require.NoError(t, err)
		assert.True(t, response.IsValid)
	})

	t.Run("ValidateInvalidRange", func(t *testing.T) {
		answerData := map[string]interface{}{
			"min": 5000000.0,
			"max": 1000000.0, // Max less than min
		}
		response, err := questionEngine.ValidateResponse(ctx, "budget_range", "", answerData)
		require.NoError(t, err)
		assert.False(t, response.IsValid)
	})

	t.Run("ValidateBoolean", func(t *testing.T) {
		response, err := questionEngine.ValidateResponse(ctx, "parking_required", "yes", nil)
		require.NoError(t, err)
		assert.True(t, response.IsValid)

		response, err = questionEngine.ValidateResponse(ctx, "parking_required", "no", nil)
		require.NoError(t, err)
		assert.True(t, response.IsValid)

		response, err = questionEngine.ValidateResponse(ctx, "parking_required", "maybe", nil)
		require.NoError(t, err)
		assert.False(t, response.IsValid)
	})
}
