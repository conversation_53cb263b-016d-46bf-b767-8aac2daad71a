package chat

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/config"
	"realestate-platform/internal/services/chat"
)

// <PERSON><PERSON> handles chat-related HTTP requests
type Handler struct {
	service *chat.Service
	config  *config.Config
	logger  *zap.Logger
}

// NewHandler creates a new chat handler
func NewHandler(service *chat.Service, config *config.Config, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		config:  config,
		logger:  logger,
	}
}

// CreateSession creates a new chat session
// @Summary Create a new chat session
// @Description Creates a new chat session for the authenticated user
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 201 {object} models.ChatSessionResponse
// @Failure 400 {object} gin.H
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions [post]
func (h *Handler) CreateSession(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	uid, ok := userID.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID type in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
		return
	}

	session, err := h.service.CreateSession(c.Request.Context(), uid)
	if err != nil {
		h.logger.Error("Failed to create chat session", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create session"})
		return
	}

	h.logger.Info("Chat session created",
		zap.String("user_id", uid.Hex()),
		zap.String("session_id", session.SessionID),
	)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    session,
	})
}

// GetUserSessions retrieves user's chat sessions
// @Summary Get user's chat sessions
// @Description Retrieves all chat sessions for the authenticated user
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limit number of sessions" default(10)
// @Success 200 {array} models.ChatSessionResponse
// @Failure 401 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions [get]
func (h *Handler) GetUserSessions(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	uid, ok := userID.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID type in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
		return
	}

	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50 // Maximum limit
	}

	sessions, err := h.service.GetUserSessions(c.Request.Context(), uid, limit)
	if err != nil {
		h.logger.Error("Failed to get user sessions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get sessions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sessions,
		"count":   len(sessions),
	})
}

// GetSession retrieves a specific chat session
// @Summary Get a specific chat session
// @Description Retrieves details of a specific chat session
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Session ID"
// @Success 200 {object} models.ChatSessionResponse
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions/{id} [get]
func (h *Handler) GetSession(c *gin.Context) {
	sessionID := c.Param("id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session ID is required"})
		return
	}

	session, err := h.service.GetSession(c.Request.Context(), sessionID)
	if err != nil {
		if err == chat.ErrSessionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "session not found"})
			return
		}
		h.logger.Error("Failed to get session", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get session"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    session,
	})
}

// DeleteSession deletes a chat session
// @Summary Delete a chat session
// @Description Deletes a specific chat session
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Session ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions/{id} [delete]
func (h *Handler) DeleteSession(c *gin.Context) {
	sessionID := c.Param("id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session ID is required"})
		return
	}

	err := h.service.DeleteSession(c.Request.Context(), sessionID)
	if err != nil {
		if err == chat.ErrSessionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "session not found"})
			return
		}
		h.logger.Error("Failed to delete session", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete session"})
		return
	}

	h.logger.Info("Chat session deleted", zap.String("session_id", sessionID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "session deleted successfully",
	})
}

// GetChatHistory retrieves chat history for a session
// @Summary Get chat history
// @Description Retrieves the complete chat history for a session
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Session ID"
// @Success 200 {object} models.ChatHistoryResponse
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions/{id}/history [get]
func (h *Handler) GetChatHistory(c *gin.Context) {
	sessionID := c.Param("id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session ID is required"})
		return
	}

	history, err := h.service.GetChatHistory(c.Request.Context(), sessionID)
	if err != nil {
		if err == chat.ErrSessionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "session not found"})
			return
		}
		h.logger.Error("Failed to get chat history", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get chat history"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// RestartSession restarts a chat session
// @Summary Restart a chat session
// @Description Restarts a chat session and begins the conversation flow again
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Session ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/sessions/{id}/restart [post]
func (h *Handler) RestartSession(c *gin.Context) {
	sessionID := c.Param("id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session ID is required"})
		return
	}

	err := h.service.RestartSession(c.Request.Context(), sessionID)
	if err != nil {
		if err == chat.ErrSessionNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "session not found"})
			return
		}
		h.logger.Error("Failed to restart session", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to restart session"})
		return
	}

	h.logger.Info("Chat session restarted", zap.String("session_id", sessionID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "session restarted successfully",
	})
}

// InitializeQuestions initializes default questions (admin only)
// @Summary Initialize default questions
// @Description Initializes the default set of property-related questions
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /chat/questions/initialize [post]
func (h *Handler) InitializeQuestions(c *gin.Context) {
	err := h.service.InitializeQuestions(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to initialize questions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to initialize questions"})
		return
	}

	h.logger.Info("Questions initialized successfully")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "questions initialized successfully",
	})
}

// GetConnectionStats returns WebSocket connection statistics
// @Summary Get WebSocket connection statistics
// @Description Returns current WebSocket connection statistics
// @Tags chat
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} gin.H
// @Router /chat/stats [get]
func (h *Handler) GetConnectionStats(c *gin.Context) {
	hub := h.service.GetHub()
	
	stats := gin.H{
		"total_connections": hub.GetActiveSessionsCount(),
		"max_connections":   1000, // From websocket constants
	}

	// If user is authenticated, include their connection count
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(primitive.ObjectID); ok {
			stats["user_connections"] = hub.GetUserSessionsCount(uid.Hex())
			stats["max_user_connections"] = 3 // From websocket constants
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
