package websocket

import (
	"errors"
)

// WebSocket service errors
var (
	ErrSessionNotFound     = errors.New("session not found")
	ErrClientNotReachable  = errors.New("client not reachable")
	ErrUserNotConnected    = errors.New("user not connected")
	ErrInvalidMessage      = errors.New("invalid message format")
	ErrUnauthorized        = errors.New("unauthorized access")
	ErrSessionExpired      = errors.New("session expired")
	ErrMaxConnectionsReached = errors.New("maximum connections reached")
)

// MessageType constants for WebSocket communication
const (
	// Client to Server message types
	MessageTypeUserResponse    = "user_response"
	MessageTypeRequestDetails  = "request_details"
	MessageTypeRestartSession  = "restart_session"
	MessageTypePing           = "ping"

	// Server to Client message types
	MessageTypeQuestion       = "question"
	MessageTypeRecommendation = "recommendation"
	MessageTypeWelcome        = "welcome"
	MessageTypeError          = "error"
	MessageTypePong           = "pong"
	MessageTypeSystem         = "system"
	MessageTypeSessionEnd     = "session_end"
)

// Connection limits and configuration
const (
	MaxConnectionsPerUser = 3
	MaxTotalConnections   = 1000
	SessionTimeout        = 3600 // 1 hour in seconds
)
