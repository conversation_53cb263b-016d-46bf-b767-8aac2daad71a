package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ChatSession represents a chat conversation session between user and system
type ChatSession struct {
	ID        primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	UserID    primitive.ObjectID     `json:"user_id" bson:"user_id"`
	SessionID string                 `json:"session_id" bson:"session_id"` // UUID for WebSocket identification
	Status    string                 `json:"status" bson:"status"`         // active, completed, abandoned, expired
	CreatedAt time.Time              `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time              `json:"updated_at" bson:"updated_at"`
	ExpiresAt time.Time              `json:"expires_at" bson:"expires_at"`
	Metadata  map[string]interface{} `json:"metadata" bson:"metadata"` // Additional session data
}

// ChatMessage represents individual messages in a chat session
type ChatMessage struct {
	ID          primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	SessionID   primitive.ObjectID     `json:"session_id" bson:"session_id"`
	MessageType string                 `json:"message_type" bson:"message_type"` // question, response, system, recommendation
	Content     string                 `json:"content" bson:"content"`
	Data        map[string]interface{} `json:"data" bson:"data"` // Additional message data (options, property details, etc.)
	Timestamp   time.Time              `json:"timestamp" bson:"timestamp"`
	IsSystem    bool                   `json:"is_system" bson:"is_system"`
}

// UserResponse represents user's answer to a specific question
type UserResponse struct {
	ID          primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	SessionID   primitive.ObjectID     `json:"session_id" bson:"session_id"`
	QuestionID  string                 `json:"question_id" bson:"question_id"`
	Answer      string                 `json:"answer" bson:"answer"`
	AnswerData  map[string]interface{} `json:"answer_data" bson:"answer_data"` // Structured answer data
	RespondedAt time.Time              `json:"responded_at" bson:"responded_at"`
}

// PropertyRecommendation represents a property recommendation for a user
type PropertyRecommendation struct {
	ID               primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	SessionID        primitive.ObjectID     `json:"session_id" bson:"session_id"`
	PropertyID       primitive.ObjectID     `json:"property_id" bson:"property_id"`
	Score            float64                `json:"score" bson:"score"`
	MatchingCriteria map[string]interface{} `json:"matching_criteria" bson:"matching_criteria"`
	CreatedAt        time.Time              `json:"created_at" bson:"created_at"`
}

// ChatSessionStatus constants
const (
	ChatSessionStatusActive    = "active"
	ChatSessionStatusCompleted = "completed"
	ChatSessionStatusAbandoned = "abandoned"
	ChatSessionStatusExpired   = "expired"
)

// MessageType constants
const (
	MessageTypeQuestion       = "question"
	MessageTypeResponse       = "response"
	MessageTypeSystem         = "system"
	MessageTypeRecommendation = "recommendation"
	MessageTypeWelcome        = "welcome"
	MessageTypeError          = "error"
)

// CreateChatSessionRequest represents the request to create a new chat session
type CreateChatSessionRequest struct {
	UserID primitive.ObjectID `json:"user_id" binding:"required"`
}

// ChatSessionResponse represents the response when creating/retrieving a chat session
type ChatSessionResponse struct {
	ID        primitive.ObjectID `json:"id"`
	SessionID string             `json:"session_id"`
	Status    string             `json:"status"`
	CreatedAt time.Time          `json:"created_at"`
	ExpiresAt time.Time          `json:"expires_at"`
}

// ChatHistoryResponse represents the chat history for a session
type ChatHistoryResponse struct {
	Session   ChatSessionResponse `json:"session"`
	Messages  []ChatMessage       `json:"messages"`
	Responses []UserResponse      `json:"responses"`
}

// WebSocketMessage represents messages sent over WebSocket
type WebSocketMessage struct {
	Type      string                 `json:"type"`
	SessionID string                 `json:"session_id,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Error     string                 `json:"error,omitempty"`
}

// UserResponseMessage represents user's response via WebSocket
type UserResponseMessage struct {
	Type       string                 `json:"type"`
	SessionID  string                 `json:"session_id"`
	QuestionID string                 `json:"question_id"`
	Answer     string                 `json:"answer"`
	AnswerData map[string]interface{} `json:"answer_data,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
}

// QuestionMessage represents a question sent to user via WebSocket
type QuestionMessage struct {
	Type         string                 `json:"type"`
	QuestionID   string                 `json:"question_id"`
	Text         string                 `json:"text"`
	QuestionType string                 `json:"question_type"`
	Options      []string               `json:"options,omitempty"`
	Validation   map[string]interface{} `json:"validation,omitempty"`
	Category     string                 `json:"category"`
	IsRequired   bool                   `json:"is_required"`
}

// RecommendationMessage represents property recommendations sent via WebSocket
type RecommendationMessage struct {
	Type            string                 `json:"type"`
	Properties      []PropertyWithScore    `json:"properties"`
	TotalMatches    int                    `json:"total_matches"`
	SessionComplete bool                   `json:"session_complete"`
	SearchCriteria  map[string]interface{} `json:"search_criteria"`
}

// PropertyWithScore represents a property with its recommendation score
type PropertyWithScore struct {
	Property
	Score            float64                `json:"score"`
	MatchingCriteria map[string]interface{} `json:"matching_criteria"`
	MatchReasons     []string               `json:"match_reasons"`
}
