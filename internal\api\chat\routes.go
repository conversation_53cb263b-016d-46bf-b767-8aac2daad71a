package chat

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"realestate-platform/internal/config"
	"realestate-platform/internal/repository/mongodb"
	"realestate-platform/internal/services/chat"
)

// RegisterRoutes registers chat-related routes
func RegisterRoutes(router *gin.RouterGroup, db *mongodb.MongoDBClient, redisClient *redis.Client, config *config.Config, logger *zap.Logger) {
	// Create chat service
	chatService := chat.NewService(db, redisClient, logger)

	// Initialize default questions on startup
	go func() {
		ctx := context.Background()
		if err := chatService.InitializeQuestions(ctx); err != nil {
			logger.Error("Failed to initialize default questions", zap.Error(err))
		}
	}()

	// Create message handler
	messageHandler := chat.NewMessageHandler(chatService)

	// Create handlers
	handler := NewHandler(chatService, config, logger)
	wsHandler := NewWebSocketHandler(chatService.GetHub(), messageHandler, config, logger)

	// Chat session routes
	chatGroup := router.Group("/chat")
	{
		// REST API endpoints
		chatGroup.POST("/sessions", handler.CreateSession)
		chatGroup.GET("/sessions", handler.GetUserSessions)
		chatGroup.GET("/sessions/:id", handler.GetSession)
		chatGroup.DELETE("/sessions/:id", handler.DeleteSession)
		chatGroup.GET("/sessions/:id/history", handler.GetChatHistory)
		chatGroup.POST("/sessions/:id/restart", handler.RestartSession)

		// WebSocket endpoint
		chatGroup.GET("/ws/:session_id", wsHandler.HandleWebSocket)

		// Statistics endpoint
		chatGroup.GET("/stats", handler.GetConnectionStats)

		// Admin endpoints (could be moved to admin group later)
		chatGroup.POST("/questions/initialize", handler.InitializeQuestions)
	}

	// Start the WebSocket hub
	go chatService.GetHub().Run()

	logger.Info("Chat routes registered successfully")
}
