package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Question represents a property-related question template
type Question struct {
	ID              primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	QuestionID      string                 `json:"question_id" bson:"question_id"` // Unique identifier for the question
	Text            string                 `json:"text" bson:"text"`
	QuestionType    string                 `json:"question_type" bson:"question_type"` // single_choice, multiple_choice, text, number, range
	Options         []string               `json:"options" bson:"options"`             // For choice-based questions
	Category        string                 `json:"category" bson:"category"`           // budget, location, specifications, amenities, timeline
	Order           int                    `json:"order" bson:"order"`                 // Question order in the flow
	ValidationRules map[string]interface{} `json:"validation_rules" bson:"validation_rules"`
	IsActive        bool                   `json:"is_active" bson:"is_active"`
	IsRequired      bool                   `json:"is_required" bson:"is_required"`
	CreatedAt       time.Time              `json:"created_at" bson:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at" bson:"updated_at"`
}

// QuestionType constants
const (
	QuestionTypeSingleChoice   = "single_choice"
	QuestionTypeMultipleChoice = "multiple_choice"
	QuestionTypeText           = "text"
	QuestionTypeNumber         = "number"
	QuestionTypeRange          = "range"
	QuestionTypeBoolean        = "boolean"
)

// QuestionCategory constants
const (
	QuestionCategoryBudget         = "budget"
	QuestionCategoryLocation       = "location"
	QuestionCategorySpecifications = "specifications"
	QuestionCategoryAmenities      = "amenities"
	QuestionCategoryTimeline       = "timeline"
	QuestionCategoryPreferences    = "preferences"
)

// CreateQuestionRequest represents the request to create a new question
type CreateQuestionRequest struct {
	QuestionID      string                 `json:"question_id" binding:"required"`
	Text            string                 `json:"text" binding:"required"`
	QuestionType    string                 `json:"question_type" binding:"required"`
	Options         []string               `json:"options"`
	Category        string                 `json:"category" binding:"required"`
	Order           int                    `json:"order" binding:"required"`
	ValidationRules map[string]interface{} `json:"validation_rules"`
	IsRequired      bool                   `json:"is_required"`
}

// UpdateQuestionRequest represents the request to update a question
type UpdateQuestionRequest struct {
	Text            string                 `json:"text"`
	QuestionType    string                 `json:"question_type"`
	Options         []string               `json:"options"`
	Category        string                 `json:"category"`
	Order           int                    `json:"order"`
	ValidationRules map[string]interface{} `json:"validation_rules"`
	IsRequired      bool                   `json:"is_required"`
	IsActive        bool                   `json:"is_active"`
}

// QuestionFlow represents the flow of questions for a chat session
type QuestionFlow struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name        string             `json:"name" bson:"name"`
	Description string             `json:"description" bson:"description"`
	Questions   []string           `json:"questions" bson:"questions"` // Array of question IDs in order
	IsActive    bool               `json:"is_active" bson:"is_active"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`
}

// QuestionResponse represents a structured response to a question
type QuestionResponse struct {
	QuestionID   string                 `json:"question_id"`
	Answer       string                 `json:"answer"`
	AnswerData   map[string]interface{} `json:"answer_data"`
	IsValid      bool                   `json:"is_valid"`
	ErrorMessage string                 `json:"error_message,omitempty"`
}

// Default question templates for property search
var DefaultQuestions = []Question{
	{
		QuestionID:   "budget_range",
		Text:         "What's your budget range for the property?",
		QuestionType: QuestionTypeRange,
		Category:     QuestionCategoryBudget,
		Order:        1,
		IsRequired:   true,
		IsActive:     true,
		ValidationRules: map[string]interface{}{
			"min": 0,
			"max": 100000000,
		},
	},
	{
		QuestionID:   "property_type",
		Text:         "What type of property are you looking for?",
		QuestionType: QuestionTypeSingleChoice,
		Options:      []string{"Apartment", "House", "Villa", "Studio", "Penthouse"},
		Category:     QuestionCategorySpecifications,
		Order:        2,
		IsRequired:   true,
		IsActive:     true,
	},
	{
		QuestionID:   "bhk_preference",
		Text:         "How many bedrooms do you need?",
		QuestionType: QuestionTypeSingleChoice,
		Options:      []string{"1 BHK", "2 BHK", "3 BHK", "4 BHK", "5+ BHK"},
		Category:     QuestionCategorySpecifications,
		Order:        3,
		IsRequired:   true,
		IsActive:     true,
	},
	{
		QuestionID:   "preferred_locations",
		Text:         "Which areas or cities are you interested in?",
		QuestionType: QuestionTypeMultipleChoice,
		Category:     QuestionCategoryLocation,
		Order:        4,
		IsRequired:   true,
		IsActive:     true,
	},
	{
		QuestionID:   "property_status",
		Text:         "Are you looking to buy or rent?",
		QuestionType: QuestionTypeSingleChoice,
		Options:      []string{"Buy", "Rent"},
		Category:     QuestionCategoryPreferences,
		Order:        5,
		IsRequired:   true,
		IsActive:     true,
	},
	{
		QuestionID:   "parking_required",
		Text:         "Do you need parking space?",
		QuestionType: QuestionTypeBoolean,
		Category:     QuestionCategoryAmenities,
		Order:        6,
		IsRequired:   false,
		IsActive:     true,
	},
	{
		QuestionID:   "preferred_amenities",
		Text:         "Which amenities are important to you?",
		QuestionType: QuestionTypeMultipleChoice,
		Options:      []string{"Gym", "Swimming Pool", "Security", "Garden", "Elevator", "Power Backup"},
		Category:     QuestionCategoryAmenities,
		Order:        7,
		IsRequired:   false,
		IsActive:     true,
	},
	{
		QuestionID:   "move_in_timeline",
		Text:         "When are you planning to move in?",
		QuestionType: QuestionTypeSingleChoice,
		Options:      []string{"Immediately", "Within 1 month", "1-3 months", "3-6 months", "6+ months"},
		Category:     QuestionCategoryTimeline,
		Order:        8,
		IsRequired:   false,
		IsActive:     true,
	},
}
