package websocket

import (
	"context"
	"encoding/json"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
)

// Hub maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Inbound messages from the clients
	broadcast chan []byte

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Redis client for session management
	redisClient *redis.Client

	// Logger
	logger *zap.Logger

	// Mutex for thread-safe operations
	mutex sync.RWMutex

	// Session to client mapping
	sessionClients map[string]*Client

	// User to clients mapping (for multiple sessions per user)
	userClients map[string][]*Client
}

// NewHub creates a new WebSocket hub
func NewHub(redisClient *redis.Client, logger *zap.Logger) *Hub {
	return &Hub{
		clients:        make(map[*Client]bool),
		broadcast:      make(chan []byte),
		register:       make(chan *Client),
		unregister:     make(chan *Client),
		redisClient:    redisClient,
		logger:         logger,
		sessionClients: make(map[string]*Client),
		userClients:    make(map[string][]*Client),
	}
}

// Run starts the hub and handles client registration/unregistration
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)
		}
	}
}

// registerClient registers a new client
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true
	h.sessionClients[client.SessionID] = client

	// Add to user clients mapping
	userID := client.UserID.Hex()
	h.userClients[userID] = append(h.userClients[userID], client)

	h.logger.Info("Client registered",
		zap.String("user_id", client.UserID.Hex()),
		zap.String("session_id", client.SessionID),
		zap.Int("total_clients", len(h.clients)),
	)

	// Store session in Redis
	h.storeSessionInRedis(client)

	// Send welcome message
	h.sendWelcomeMessage(client)
}

// unregisterClient unregisters a client
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		delete(h.sessionClients, client.SessionID)

		// Remove from user clients mapping
		userID := client.UserID.Hex()
		userClients := h.userClients[userID]
		for i, c := range userClients {
			if c == client {
				h.userClients[userID] = append(userClients[:i], userClients[i+1:]...)
				break
			}
		}

		// Clean up empty user mapping
		if len(h.userClients[userID]) == 0 {
			delete(h.userClients, userID)
		}

		close(client.send)

		h.logger.Info("Client unregistered",
			zap.String("user_id", client.UserID.Hex()),
			zap.String("session_id", client.SessionID),
			zap.Int("total_clients", len(h.clients)),
		)

		// Update session status in Redis
		h.updateSessionStatus(client.SessionID, models.ChatSessionStatusAbandoned)
	}
}

// broadcastMessage broadcasts a message to all clients
func (h *Hub) broadcastMessage(message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

// SendToSession sends a message to a specific session
func (h *Hub) SendToSession(sessionID string, message models.WebSocketMessage) error {
	h.mutex.RLock()
	client, exists := h.sessionClients[sessionID]
	h.mutex.RUnlock()

	if !exists {
		h.logger.Warn("Session not found", zap.String("session_id", sessionID))
		return ErrSessionNotFound
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		h.logger.Error("Failed to marshal message", zap.Error(err))
		return err
	}

	select {
	case client.send <- messageBytes:
		return nil
	default:
		h.logger.Warn("Failed to send message to client", zap.String("session_id", sessionID))
		return ErrClientNotReachable
	}
}

// SendToUser sends a message to all sessions of a user
func (h *Hub) SendToUser(userID string, message models.WebSocketMessage) error {
	h.mutex.RLock()
	clients, exists := h.userClients[userID]
	h.mutex.RUnlock()

	if !exists || len(clients) == 0 {
		h.logger.Warn("No active sessions for user", zap.String("user_id", userID))
		return ErrUserNotConnected
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		h.logger.Error("Failed to marshal message", zap.Error(err))
		return err
	}

	for _, client := range clients {
		select {
		case client.send <- messageBytes:
		default:
			h.logger.Warn("Failed to send message to client",
				zap.String("user_id", userID),
				zap.String("session_id", client.SessionID),
			)
		}
	}

	return nil
}

// GetActiveSessionsCount returns the number of active sessions
func (h *Hub) GetActiveSessionsCount() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}

// GetUserSessionsCount returns the number of active sessions for a user
func (h *Hub) GetUserSessionsCount(userID string) int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.userClients[userID])
}

// storeSessionInRedis stores session information in Redis
func (h *Hub) storeSessionInRedis(client *Client) {
	ctx := context.Background()
	sessionKey := "chat:session:" + client.SessionID

	sessionData := map[string]interface{}{
		"user_id":    client.UserID.Hex(),
		"session_id": client.SessionID,
		"status":     models.ChatSessionStatusActive,
		"created_at": time.Now(),
		"expires_at": time.Now().Add(time.Hour), // 1 hour expiry
	}

	sessionJSON, err := json.Marshal(sessionData)
	if err != nil {
		h.logger.Error("Failed to marshal session data", zap.Error(err))
		return
	}

	err = h.redisClient.Set(ctx, sessionKey, sessionJSON, time.Hour).Err()
	if err != nil {
		h.logger.Error("Failed to store session in Redis", zap.Error(err))
	}
}

// updateSessionStatus updates session status in Redis
func (h *Hub) updateSessionStatus(sessionID, status string) {
	ctx := context.Background()
	sessionKey := "chat:session:" + sessionID

	// Get existing session data
	sessionJSON, err := h.redisClient.Get(ctx, sessionKey).Result()
	if err != nil {
		h.logger.Error("Failed to get session from Redis", zap.Error(err))
		return
	}

	var sessionData map[string]interface{}
	if err := json.Unmarshal([]byte(sessionJSON), &sessionData); err != nil {
		h.logger.Error("Failed to unmarshal session data", zap.Error(err))
		return
	}

	// Update status
	sessionData["status"] = status
	sessionData["updated_at"] = time.Now()

	updatedJSON, err := json.Marshal(sessionData)
	if err != nil {
		h.logger.Error("Failed to marshal updated session data", zap.Error(err))
		return
	}

	err = h.redisClient.Set(ctx, sessionKey, updatedJSON, time.Hour).Err()
	if err != nil {
		h.logger.Error("Failed to update session in Redis", zap.Error(err))
	}
}

// sendWelcomeMessage sends a welcome message to a newly connected client
func (h *Hub) sendWelcomeMessage(client *Client) {
	welcomeMessage := models.WebSocketMessage{
		Type:      models.MessageTypeWelcome,
		SessionID: client.SessionID,
		Data: map[string]interface{}{
			"message": "Welcome to the property recommendation chat! I'll help you find the perfect property by asking a few questions.",
		},
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(welcomeMessage)
	if err != nil {
		h.logger.Error("Failed to marshal welcome message", zap.Error(err))
		return
	}

	select {
	case client.send <- messageBytes:
	default:
		h.logger.Warn("Failed to send welcome message", zap.String("session_id", client.SessionID))
	}
}
