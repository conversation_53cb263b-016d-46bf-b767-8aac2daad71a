# PowerShell script to test the Real Estate Chat System API

$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
}

Write-Host "🏠 Testing Real Estate Chat System API" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Step 1: Register a new user
Write-Host "`n1. Registering a new user..." -ForegroundColor Yellow
$registerData = @{
    first_name = "<PERSON>"
    last_name = "<PERSON>e"
    email = "<EMAIL>"
    password = "password123"
    phone_number = "+91 9876543210"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/auth/register" -Method POST -Body $registerData -Headers $headers
    Write-Host "✅ User registered successfully" -ForegroundColor Green
    Write-Host "User ID: $($registerResponse.data.user.id)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Registration failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response.StatusCode -eq 409) {
        Write-Host "User might already exist, continuing with login..." -ForegroundColor Yellow
    } else {
        exit 1
    }
}

# Step 2: Login to get JWT token
Write-Host "`n2. Logging in to get JWT token..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/auth/login" -Method POST -Body $loginData -Headers $headers
    $jwtToken = $loginResponse.data.token
    Write-Host "✅ Login successful" -ForegroundColor Green
    Write-Host "JWT Token: $($jwtToken.Substring(0, 50))..." -ForegroundColor Cyan
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Update headers with JWT token
$authHeaders = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $jwtToken"
}

# Step 3: Initialize default questions (admin function)
Write-Host "`n3. Initializing default questions..." -ForegroundColor Yellow
try {
    $initResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/chat/questions/initialize" -Method POST -Headers $authHeaders
    Write-Host "✅ Questions initialized successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Questions might already be initialized: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 4: Create a new chat session
Write-Host "`n4. Creating a new chat session..." -ForegroundColor Yellow
try {
    $sessionResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/chat/sessions" -Method POST -Headers $authHeaders
    $sessionId = $sessionResponse.data.session_id
    Write-Host "✅ Chat session created successfully" -ForegroundColor Green
    Write-Host "Session ID: $sessionId" -ForegroundColor Cyan
    Write-Host "Session Status: $($sessionResponse.data.status)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create chat session: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Get user's chat sessions
Write-Host "`n5. Getting user's chat sessions..." -ForegroundColor Yellow
try {
    $sessionsResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/chat/sessions" -Method GET -Headers $authHeaders
    Write-Host "✅ Retrieved chat sessions successfully" -ForegroundColor Green
    Write-Host "Number of sessions: $($sessionsResponse.count)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get chat sessions: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Get specific session details
Write-Host "`n6. Getting specific session details..." -ForegroundColor Yellow
try {
    $sessionDetailResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/chat/sessions/$sessionId" -Method GET -Headers $authHeaders
    Write-Host "✅ Retrieved session details successfully" -ForegroundColor Green
    Write-Host "Session ID: $($sessionDetailResponse.data.session_id)" -ForegroundColor Cyan
    Write-Host "Created At: $($sessionDetailResponse.data.created_at)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get session details: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Get connection statistics
Write-Host "`n7. Getting connection statistics..." -ForegroundColor Yellow
try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/api/v1/chat/stats" -Method GET -Headers $authHeaders
    Write-Host "✅ Retrieved connection stats successfully" -ForegroundColor Green
    Write-Host "Total Connections: $($statsResponse.data.total_connections)" -ForegroundColor Cyan
    Write-Host "Max Connections: $($statsResponse.data.max_connections)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get connection stats: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 8: Test WebSocket connection (basic test)
Write-Host "`n8. Testing WebSocket endpoint availability..." -ForegroundColor Yellow
$wsUrl = "ws://localhost:8080/api/v1/chat/ws/$sessionId"
Write-Host "WebSocket URL: $wsUrl" -ForegroundColor Cyan
Write-Host "Note: Use the JavaScript client or React component to test WebSocket functionality" -ForegroundColor Yellow

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "✅ All REST API endpoints are working correctly" -ForegroundColor Green
Write-Host "✅ Chat session created: $sessionId" -ForegroundColor Green
Write-Host "✅ JWT Token: $($jwtToken.Substring(0, 30))..." -ForegroundColor Green
Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Use the JavaScript client (examples/frontend/chat-client.js) to test WebSocket" -ForegroundColor White
Write-Host "2. Use the React component (examples/frontend/ChatComponent.jsx) for full UI testing" -ForegroundColor White
Write-Host "3. Open http://localhost:8080/swagger/index.html to see API documentation" -ForegroundColor White

# Save session info for WebSocket testing
$sessionInfo = @{
    sessionId = $sessionId
    jwtToken = $jwtToken
    baseUrl = $baseUrl
} | ConvertTo-Json

$sessionInfo | Out-File -FilePath "chat_session_info.json" -Encoding UTF8
Write-Host "`n💾 Session info saved to chat_session_info.json for WebSocket testing" -ForegroundColor Cyan
