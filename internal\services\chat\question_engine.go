package chat

import (
	"context"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

// QuestionEngine manages the flow of questions in a chat session
type QuestionEngine struct {
	db     *mongodb.MongoDBClient
	logger *zap.Logger
}

// NewQuestionEngine creates a new question engine
func NewQuestionEngine(db *mongodb.MongoDBClient, logger *zap.Logger) *QuestionEngine {
	return &QuestionEngine{
		db:     db,
		logger: logger,
	}
}

// GetFirstQuestion returns the first question in the flow
func (qe *QuestionEngine) GetFirstQuestion(ctx context.Context) (*models.Question, error) {
	collection := qe.db.GetCollection("questions")

	filter := bson.M{
		"is_active": true,
		"order":     1,
	}

	var question models.Question
	err := collection.FindOne(ctx, filter).Decode(&question)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrNoQuestionsFound
		}
		qe.logger.Error("Failed to get first question", zap.Error(err))
		return nil, err
	}

	return &question, nil
}

// GetNextQuestion determines the next question based on current responses
func (qe *QuestionEngine) GetNextQuestion(ctx context.Context, sessionID primitive.ObjectID, currentQuestionID string) (*models.Question, error) {
	// Get all responses for this session
	responses, err := qe.getSessionResponses(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Get current question to determine next order
	currentQuestion, err := qe.getQuestionByID(ctx, currentQuestionID)
	if err != nil {
		return nil, err
	}

	// Determine next question based on logic
	nextQuestion, err := qe.determineNextQuestion(ctx, currentQuestion, responses)
	if err != nil {
		return nil, err
	}

	return nextQuestion, nil
}

// ValidateResponse validates a user's response to a question
func (qe *QuestionEngine) ValidateResponse(ctx context.Context, questionID, answer string, answerData map[string]interface{}) (*models.QuestionResponse, error) {
	question, err := qe.getQuestionByID(ctx, questionID)
	if err != nil {
		return nil, err
	}

	response := &models.QuestionResponse{
		QuestionID: questionID,
		Answer:     answer,
		AnswerData: answerData,
		IsValid:    true,
	}

	// Validate based on question type
	switch question.QuestionType {
	case models.QuestionTypeSingleChoice:
		if !qe.isValidChoice(answer, question.Options) {
			response.IsValid = false
			response.ErrorMessage = "Please select a valid option"
		}

	case models.QuestionTypeMultipleChoice:
		if !qe.isValidMultipleChoice(answer, question.Options) {
			response.IsValid = false
			response.ErrorMessage = "Please select valid options"
		}

	case models.QuestionTypeNumber:
		if !qe.isValidNumber(answer, question.ValidationRules) {
			response.IsValid = false
			response.ErrorMessage = "Please enter a valid number"
		}

	case models.QuestionTypeRange:
		if !qe.isValidRange(answerData, question.ValidationRules) {
			response.IsValid = false
			response.ErrorMessage = "Please enter a valid range"
		}

	case models.QuestionTypeText:
		if !qe.isValidText(answer, question.ValidationRules) {
			response.IsValid = false
			response.ErrorMessage = "Please enter valid text"
		}

	case models.QuestionTypeBoolean:
		if !qe.isValidBoolean(answer) {
			response.IsValid = false
			response.ErrorMessage = "Please select yes or no"
		}
	}

	// Check if required
	if question.IsRequired && (answer == "" || strings.TrimSpace(answer) == "") {
		response.IsValid = false
		response.ErrorMessage = "This question is required"
	}

	return response, nil
}

// IsConversationComplete checks if all required questions have been answered
func (qe *QuestionEngine) IsConversationComplete(ctx context.Context, sessionID primitive.ObjectID) (bool, error) {
	// Get all active questions
	questions, err := qe.getAllActiveQuestions(ctx)
	if err != nil {
		return false, err
	}

	// Get all responses for this session
	responses, err := qe.getSessionResponses(ctx, sessionID)
	if err != nil {
		return false, err
	}

	// Create a map of answered questions
	answeredQuestions := make(map[string]bool)
	for _, response := range responses {
		answeredQuestions[response.QuestionID] = true
	}

	// Check if all required questions are answered
	for _, question := range questions {
		if question.IsRequired && !answeredQuestions[question.QuestionID] {
			return false, nil
		}
	}

	return true, nil
}

// GetConversationSummary returns a summary of all responses
func (qe *QuestionEngine) GetConversationSummary(ctx context.Context, sessionID primitive.ObjectID) (map[string]interface{}, error) {
	responses, err := qe.getSessionResponses(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	summary := make(map[string]interface{})

	for _, response := range responses {
		question, err := qe.getQuestionByID(ctx, response.QuestionID)
		if err != nil {
			qe.logger.Warn("Failed to get question for response",
				zap.String("question_id", response.QuestionID),
				zap.Error(err),
			)
			continue
		}

		summary[question.Category] = map[string]interface{}{
			"question": question.Text,
			"answer":   response.Answer,
			"data":     response.AnswerData,
		}
	}

	return summary, nil
}

// InitializeDefaultQuestions creates default questions if they don't exist
func (qe *QuestionEngine) InitializeDefaultQuestions(ctx context.Context) error {
	collection := qe.db.GetCollection("questions")

	// Check if questions already exist
	count, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return err
	}

	if count > 0 {
		qe.logger.Info("Questions already exist, skipping initialization")
		return nil
	}

	// Insert default questions
	var documents []interface{}
	for _, question := range models.DefaultQuestions {
		question.ID = primitive.NewObjectID()
		question.CreatedAt = time.Now()
		question.UpdatedAt = time.Now()
		documents = append(documents, question)
	}

	_, err = collection.InsertMany(ctx, documents)
	if err != nil {
		qe.logger.Error("Failed to initialize default questions", zap.Error(err))
		return err
	}

	qe.logger.Info("Default questions initialized", zap.Int("count", len(documents)))
	return nil
}

// Helper methods

func (qe *QuestionEngine) getQuestionByID(ctx context.Context, questionID string) (*models.Question, error) {
	collection := qe.db.GetCollection("questions")

	var question models.Question
	err := collection.FindOne(ctx, bson.M{"question_id": questionID}).Decode(&question)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrQuestionNotFound
		}
		return nil, err
	}

	return &question, nil
}

func (qe *QuestionEngine) getSessionResponses(ctx context.Context, sessionID primitive.ObjectID) ([]models.UserResponse, error) {
	collection := qe.db.GetCollection("user_responses")

	filter := bson.M{"session_id": sessionID}
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var responses []models.UserResponse
	if err := cursor.All(ctx, &responses); err != nil {
		return nil, err
	}

	return responses, nil
}

func (qe *QuestionEngine) getAllActiveQuestions(ctx context.Context) ([]models.Question, error) {
	collection := qe.db.GetCollection("questions")

	filter := bson.M{"is_active": true}
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var questions []models.Question
	if err := cursor.All(ctx, &questions); err != nil {
		return nil, err
	}

	return questions, nil
}

func (qe *QuestionEngine) determineNextQuestion(ctx context.Context, currentQuestion *models.Question, responses []models.UserResponse) (*models.Question, error) {
	// Simple linear flow for now - get next question by order
	collection := qe.db.GetCollection("questions")

	filter := bson.M{
		"is_active": true,
		"order":     bson.M{"$gt": currentQuestion.Order},
	}

	var nextQuestion models.Question
	err := collection.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: "order", Value: 1}})).Decode(&nextQuestion)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrNoMoreQuestions
		}
		return nil, err
	}

	return &nextQuestion, nil
}

// Validation helper methods

func (qe *QuestionEngine) isValidChoice(answer string, options []string) bool {
	for _, option := range options {
		if strings.EqualFold(answer, option) {
			return true
		}
	}
	return false
}

func (qe *QuestionEngine) isValidMultipleChoice(answer string, options []string) bool {
	choices := strings.Split(answer, ",")
	for _, choice := range choices {
		choice = strings.TrimSpace(choice)
		if !qe.isValidChoice(choice, options) {
			return false
		}
	}
	return true
}

func (qe *QuestionEngine) isValidNumber(answer string, rules map[string]interface{}) bool {
	num, err := strconv.ParseFloat(answer, 64)
	if err != nil {
		return false
	}

	if min, ok := rules["min"].(float64); ok && num < min {
		return false
	}

	if max, ok := rules["max"].(float64); ok && num > max {
		return false
	}

	return true
}

func (qe *QuestionEngine) isValidRange(answerData map[string]interface{}, rules map[string]interface{}) bool {
	minVal, minOk := answerData["min"]
	maxVal, maxOk := answerData["max"]

	if !minOk || !maxOk {
		return false
	}

	min, minFloat := minVal.(float64)
	max, maxFloat := maxVal.(float64)

	if !minFloat || !maxFloat {
		return false
	}

	if min >= max {
		return false
	}

	if ruleMin, ok := rules["min"].(float64); ok && min < ruleMin {
		return false
	}

	if ruleMax, ok := rules["max"].(float64); ok && max > ruleMax {
		return false
	}

	return true
}

func (qe *QuestionEngine) isValidText(answer string, rules map[string]interface{}) bool {
	if minLen, ok := rules["min_length"].(float64); ok && len(answer) < int(minLen) {
		return false
	}

	if maxLen, ok := rules["max_length"].(float64); ok && len(answer) > int(maxLen) {
		return false
	}

	return true
}

func (qe *QuestionEngine) isValidBoolean(answer string) bool {
	answer = strings.ToLower(strings.TrimSpace(answer))
	return answer == "yes" || answer == "no" || answer == "true" || answer == "false" || answer == "1" || answer == "0"
}
