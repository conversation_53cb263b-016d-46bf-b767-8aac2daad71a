# 🏠 Real Estate Chat System Documentation

## Overview

The Real Estate Chat System is a sophisticated, real-time property recommendation engine that uses WebSocket connections to provide personalized property suggestions through an interactive conversation flow.

## 🏗️ Architecture

### Core Components

1. **WebSocket Server** - Real-time bidirectional communication
2. **Question Engine** - Dynamic property-related question management  
3. **Chat Session Manager** - Redis-backed session state management
4. **Property Recommendation Engine** - AI-powered property matching
5. **Concurrent Processing Pool** - Efficient goroutine management

### Technology Stack

- **WebSocket**: `gorilla/websocket` for real-time communication
- **Session Management**: Redis for distributed session storage
- **Database**: MongoDB for chat history and question templates
- **Concurrency**: Worker pools with channels and goroutines
- **Authentication**: JWT integration with existing auth middleware
- **Caching**: Redis for performance optimization

## 📋 API Endpoints

### REST API

```
POST   /api/v1/chat/sessions              # Create new chat session
GET    /api/v1/chat/sessions              # Get user's chat sessions  
GET    /api/v1/chat/sessions/{id}         # Get specific session
DELETE /api/v1/chat/sessions/{id}         # Delete session
GET    /api/v1/chat/sessions/{id}/history # Get chat history
POST   /api/v1/chat/sessions/{id}/restart # Restart conversation
GET    /api/v1/chat/stats                 # Get connection statistics
POST   /api/v1/chat/questions/initialize  # Initialize default questions (admin)
```

### WebSocket Endpoint

```
WS     /api/v1/chat/ws/{session_id}       # WebSocket connection
```

## 🔌 WebSocket Message Protocol

### Client to Server Messages

#### User Response
```json
{
  "type": "user_response",
  "session_id": "uuid",
  "question_id": "budget_range",
  "answer": "2-3 BHK",
  "answer_data": {
    "min": 1000000,
    "max": 5000000
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

#### Request Property Details
```json
{
  "type": "request_details",
  "property_id": "property_uuid"
}
```

#### Restart Session
```json
{
  "type": "restart_session"
}
```

### Server to Client Messages

#### Question
```json
{
  "type": "question",
  "session_id": "uuid",
  "data": {
    "question_id": "budget_range",
    "text": "What's your budget range for the property?",
    "question_type": "range",
    "options": [],
    "category": "budget",
    "is_required": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

#### Property Recommendations
```json
{
  "type": "recommendation",
  "session_id": "uuid",
  "data": {
    "properties": [
      {
        "id": "property_id",
        "title": "3 BHK Apartment",
        "total_price": 2500000,
        "location": {
          "city": "Mumbai",
          "area": "Bandra"
        },
        "score": 0.85,
        "matching_criteria": {
          "budget_match": true,
          "location_match": true
        },
        "match_reasons": [
          "Within your budget range (₹1000000 - ₹5000000)",
          "Located in your preferred area: Bandra"
        ]
      }
    ],
    "total_matches": 10,
    "session_complete": true
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

#### Welcome Message
```json
{
  "type": "welcome",
  "session_id": "uuid",
  "data": {
    "message": "Welcome to the property recommendation chat!"
  },
  "timestamp": "2024-01-01T10:00:00Z"
}
```

#### Error Message
```json
{
  "type": "error",
  "session_id": "uuid",
  "error": "Invalid response format",
  "timestamp": "2024-01-01T10:00:00Z"
}
```

## 🎯 Question Flow

### Default Questions

1. **Budget Range** (Required)
   - Type: `range`
   - Category: `budget`
   - Validation: Min/Max values

2. **Property Type** (Required)
   - Type: `single_choice`
   - Options: `["Apartment", "House", "Villa", "Studio", "Penthouse"]`
   - Category: `specifications`

3. **BHK Preference** (Required)
   - Type: `single_choice`
   - Options: `["1 BHK", "2 BHK", "3 BHK", "4 BHK", "5+ BHK"]`
   - Category: `specifications`

4. **Preferred Locations** (Required)
   - Type: `multiple_choice`
   - Category: `location`

5. **Property Status** (Required)
   - Type: `single_choice`
   - Options: `["Buy", "Rent"]`
   - Category: `preferences`

6. **Parking Required** (Optional)
   - Type: `boolean`
   - Category: `amenities`

7. **Preferred Amenities** (Optional)
   - Type: `multiple_choice`
   - Options: `["Gym", "Swimming Pool", "Security", "Garden", "Elevator", "Power Backup"]`
   - Category: `amenities`

8. **Move-in Timeline** (Optional)
   - Type: `single_choice`
   - Options: `["Immediately", "Within 1 month", "1-3 months", "3-6 months", "6+ months"]`
   - Category: `timeline`

## 🧮 Recommendation Algorithm

### Scoring Weights

- **Budget Match**: 30%
- **Location Match**: 25%
- **Specifications Match**: 20%
- **Amenities Match**: 15%
- **Timeline Match**: 10%

### MongoDB Aggregation Pipeline

The system uses a sophisticated aggregation pipeline to score and rank properties:

```javascript
[
  {
    $match: {
      status: { $in: ["available", "rent"] },
      total_price: { $gte: minBudget, $lte: maxBudget },
      "location.city": { $in: preferredCities }
    }
  },
  {
    $addFields: {
      budget_score: { /* Budget scoring logic */ },
      location_score: { /* Location scoring logic */ },
      spec_score: { /* Specification scoring logic */ },
      amenity_score: { /* Amenity scoring logic */ },
      timeline_score: { /* Timeline scoring logic */ }
    }
  },
  {
    $addFields: {
      total_score: { $add: ["$budget_score", "$location_score", "$spec_score", "$amenity_score", "$timeline_score"] }
    }
  },
  { $sort: { total_score: -1 } },
  { $limit: 10 }
]
```

## 🔧 Configuration

### Environment Variables

```yaml
# Redis Configuration
REDIS_ADDRESS=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# WebSocket Configuration
WEBSOCKET_READ_BUFFER_SIZE=1024
WEBSOCKET_WRITE_BUFFER_SIZE=1024
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_PING_PERIOD=54s
WEBSOCKET_PONG_WAIT=60s

# Chat Configuration
CHAT_SESSION_TIMEOUT=3600s
CHAT_MAX_QUESTIONS=15
CHAT_RECOMMENDATION_LIMIT=10
```

## 🚀 Getting Started

### 1. Prerequisites

- Go 1.19+
- MongoDB 4.4+
- Redis 6.0+
- Docker (optional)

### 2. Installation

```bash
# Install dependencies
go mod tidy

# Initialize default questions
curl -X POST http://localhost:8080/api/v1/chat/questions/initialize \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Start the Server

```bash
# Start with Docker
docker-compose up

# Or start manually
go run cmd/main.go
```

### 4. Test WebSocket Connection

```javascript
// JavaScript client example
const ws = new WebSocket('ws://localhost:8080/api/v1/chat/ws/session_id?token=YOUR_JWT_TOKEN');

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
};

ws.send(JSON.stringify({
  type: 'user_response',
  session_id: 'session_id',
  question_id: 'budget_range',
  answer: '2-3 BHK',
  answer_data: { min: 1000000, max: 5000000 },
  timestamp: new Date().toISOString()
}));
```

## 📊 Performance Metrics

### Target Performance

- **WebSocket Connection Latency**: < 100ms
- **Property Search Response Time**: < 2s
- **Concurrent Users**: 1000+
- **Memory Usage**: < 512MB per 1000 connections

### Monitoring

- Connection counts and user sessions
- Response times and error rates
- Chat completion and conversion rates
- Property recommendation accuracy

## 🔒 Security Features

- **JWT Authentication**: Secure WebSocket connections
- **Rate Limiting**: Prevent spam and abuse
- **Input Validation**: Sanitize all user inputs
- **Session Timeout**: Automatic cleanup of inactive sessions
- **CORS Protection**: Configurable origin validation

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
go test ./internal/services/chat/...

# Run with coverage
go test -cover ./internal/services/chat/...
```

### Integration Tests

```bash
# Test WebSocket connections
go test ./internal/api/chat/...

# Test recommendation engine
go test ./internal/services/chat/ -run TestRecommendationEngine
```

### Load Testing

```bash
# Test concurrent connections
go test ./tests/load/ -run TestConcurrentConnections
```

## 🐛 Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check JWT token validity
   - Verify session ID format
   - Ensure Redis is running

2. **No Property Recommendations**
   - Verify property data exists
   - Check search criteria
   - Review aggregation pipeline

3. **Session Timeout**
   - Increase session timeout in config
   - Implement session refresh mechanism

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=debug
go run cmd/main.go
```

## 📈 Future Enhancements

- Machine learning-based recommendation scoring
- Multi-language support for questions
- Voice-to-text integration
- Advanced filtering and sorting options
- Real-time property availability updates
- Integration with external property APIs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
