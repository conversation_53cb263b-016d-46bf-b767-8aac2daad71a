package chat

import (
	"context"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/websocket"
)

// MessageHandler implements the websocket.MessageHandler interface
type MessageHand<PERSON> struct {
	service *Service
}

// NewMessageHandler creates a new message handler
func NewMessageHandler(service *Service) *MessageHandler {
	return &MessageHandler{
		service: service,
	}
}

// HandleUserResponse handles user response messages
func (mh *MessageHandler) HandleUserResponse(client *websocket.Client, message models.UserResponseMessage) error {
	ctx := context.Background()
	return mh.service.HandleUserResponse(ctx, client, message)
}

// HandleRequestDetails handles property details request
func (mh *MessageHandler) HandleRequestDetails(client *websocket.Client, propertyID string) error {
	ctx := context.Background()
	return mh.service.HandleRequestDetails(ctx, client, propertyID)
}

// HandleSessionRestart handles session restart request
func (mh *MessageHandler) HandleSessionRestart(client *websocket.Client) error {
	ctx := context.Background()
	return mh.service.HandleSessionRestart(ctx, client)
}
