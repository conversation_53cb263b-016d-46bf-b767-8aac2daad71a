package chat

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

// RecommendationEngine generates property recommendations based on user responses
type RecommendationEngine struct {
	db     *mongodb.MongoDBClient
	logger *zap.Logger
}

// NewRecommendationEngine creates a new recommendation engine
func NewRecommendationEngine(db *mongodb.MongoDBClient, logger *zap.Logger) *RecommendationEngine {
	return &RecommendationEngine{
		db:     db,
		logger: logger,
	}
}

// SearchCriteria represents the parsed user preferences
type SearchCriteria struct {
	BudgetMin       float64
	BudgetMax       float64
	PropertyType    string
	BHK             int
	Locations       []string
	PropertyStatus  string
	ParkingRequired bool
	Amenities       []string
	Timeline        string
}

// GenerateRecommendations creates property recommendations based on user responses
func (re *RecommendationEngine) GenerateRecommendations(ctx context.Context, sessionID primitive.ObjectID, limit int) ([]models.PropertyWithScore, error) {
	// Get user responses
	responses, err := re.getSessionResponses(ctx, sessionID)
	if err != nil {
		return nil, err
	}

	// Parse responses into search criteria
	criteria, err := re.parseSearchCriteria(responses)
	if err != nil {
		return nil, err
	}

	// Build MongoDB aggregation pipeline
	pipeline := re.buildRecommendationPipeline(criteria, limit)

	// Execute aggregation
	collection := re.db.GetCollection("properties")
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		re.logger.Error("Failed to execute recommendation pipeline", zap.Error(err))
		return nil, err
	}
	defer cursor.Close(ctx)

	// Parse results
	var results []models.PropertyWithScore
	for cursor.Next(ctx) {
		var result struct {
			models.Property  `bson:",inline"`
			Score            float64                `bson:"total_score"`
			MatchingCriteria map[string]interface{} `bson:"matching_criteria"`
		}

		if err := cursor.Decode(&result); err != nil {
			re.logger.Error("Failed to decode recommendation result", zap.Error(err))
			continue
		}

		propertyWithScore := models.PropertyWithScore{
			Property:         result.Property,
			Score:            result.Score,
			MatchingCriteria: result.MatchingCriteria,
			MatchReasons:     re.generateMatchReasons(result.Property, criteria),
		}

		results = append(results, propertyWithScore)
	}

	re.logger.Info("Generated property recommendations",
		zap.String("session_id", sessionID.Hex()),
		zap.Int("count", len(results)),
	)

	return results, nil
}

// parseSearchCriteria converts user responses into structured search criteria
func (re *RecommendationEngine) parseSearchCriteria(responses []models.UserResponse) (*SearchCriteria, error) {
	criteria := &SearchCriteria{}

	for _, response := range responses {
		switch response.QuestionID {
		case "budget_range":
			if response.AnswerData != nil {
				if min, ok := response.AnswerData["min"].(float64); ok {
					criteria.BudgetMin = min
				}
				if max, ok := response.AnswerData["max"].(float64); ok {
					criteria.BudgetMax = max
				}
			}

		case "property_type":
			criteria.PropertyType = strings.ToLower(response.Answer)

		case "bhk_preference":
			// Extract number from "2 BHK" format
			bhkStr := strings.Fields(response.Answer)[0]
			if bhk, err := strconv.Atoi(bhkStr); err == nil {
				criteria.BHK = bhk
			}

		case "preferred_locations":
			// Split comma-separated locations
			locations := strings.Split(response.Answer, ",")
			for i, loc := range locations {
				locations[i] = strings.TrimSpace(loc)
			}
			criteria.Locations = locations

		case "property_status":
			status := strings.ToLower(response.Answer)
			if status == "buy" {
				criteria.PropertyStatus = "sell"
			} else if status == "rent" {
				criteria.PropertyStatus = "rent"
			}

		case "parking_required":
			criteria.ParkingRequired = strings.ToLower(response.Answer) == "yes" || response.Answer == "true"

		case "preferred_amenities":
			amenities := strings.Split(response.Answer, ",")
			for i, amenity := range amenities {
				amenities[i] = strings.TrimSpace(amenity)
			}
			criteria.Amenities = amenities

		case "move_in_timeline":
			criteria.Timeline = response.Answer
		}
	}

	return criteria, nil
}

// buildRecommendationPipeline creates MongoDB aggregation pipeline for recommendations
func (re *RecommendationEngine) buildRecommendationPipeline(criteria *SearchCriteria, limit int) []bson.M {
	pipeline := []bson.M{}

	// Match stage - basic filtering
	matchStage := bson.M{
		"status": bson.M{"$in": []string{"available", criteria.PropertyStatus}},
	}

	// Budget filter
	if criteria.BudgetMin > 0 || criteria.BudgetMax > 0 {
		budgetFilter := bson.M{}
		if criteria.BudgetMin > 0 {
			budgetFilter["$gte"] = criteria.BudgetMin
		}
		if criteria.BudgetMax > 0 {
			budgetFilter["$lte"] = criteria.BudgetMax
		}
		matchStage["total_price"] = budgetFilter
	}

	// Location filter
	if len(criteria.Locations) > 0 {
		matchStage["$or"] = []bson.M{
			{"location.city": bson.M{"$in": criteria.Locations}},
			{"location.area": bson.M{"$in": criteria.Locations}},
		}
	}

	pipeline = append(pipeline, bson.M{"$match": matchStage})

	// Add scoring fields
	addFieldsStage := bson.M{
		"$addFields": bson.M{
			"budget_score":   re.buildBudgetScore(criteria),
			"location_score": re.buildLocationScore(criteria),
			"spec_score":     re.buildSpecificationScore(criteria),
			"amenity_score":  re.buildAmenityScore(criteria),
			"timeline_score": re.buildTimelineScore(criteria),
			"matching_criteria": bson.M{
				"budget_match":   re.buildBudgetMatch(criteria),
				"location_match": re.buildLocationMatch(criteria),
				"spec_match":     re.buildSpecMatch(criteria),
				"amenity_match":  re.buildAmenityMatch(criteria),
			},
		},
	}

	pipeline = append(pipeline, addFieldsStage)

	// Calculate total score
	totalScoreStage := bson.M{
		"$addFields": bson.M{
			"total_score": bson.M{
				"$add": []string{
					"$budget_score",
					"$location_score",
					"$spec_score",
					"$amenity_score",
					"$timeline_score",
				},
			},
		},
	}

	pipeline = append(pipeline, totalScoreStage)

	// Sort by score
	pipeline = append(pipeline, bson.M{"$sort": bson.M{"total_score": -1}})

	// Limit results
	pipeline = append(pipeline, bson.M{"$limit": limit})

	return pipeline
}

// Scoring functions

func (re *RecommendationEngine) buildBudgetScore(criteria *SearchCriteria) bson.M {
	if criteria.BudgetMin == 0 && criteria.BudgetMax == 0 {
		return bson.M{"$literal": 0.3} // Full score if no budget specified
	}

	targetBudget := (criteria.BudgetMin + criteria.BudgetMax) / 2

	return bson.M{
		"$multiply": []interface{}{
			bson.M{
				"$subtract": []interface{}{
					1,
					bson.M{
						"$divide": []interface{}{
							bson.M{"$abs": bson.M{"$subtract": []interface{}{"$total_price", targetBudget}}},
							targetBudget,
						},
					},
				},
			},
			0.3, // 30% weight
		},
	}
}

func (re *RecommendationEngine) buildLocationScore(criteria *SearchCriteria) bson.M {
	if len(criteria.Locations) == 0 {
		return bson.M{"$literal": 0.25} // Full score if no location preference
	}

	return bson.M{
		"$cond": bson.M{
			"if": bson.M{
				"$or": []bson.M{
					{"$in": []interface{}{"$location.city", criteria.Locations}},
					{"$in": []interface{}{"$location.area", criteria.Locations}},
				},
			},
			"then": 0.25, // 25% weight
			"else": 0.1,  // Partial score for non-matching locations
		},
	}
}

func (re *RecommendationEngine) buildSpecificationScore(criteria *SearchCriteria) bson.M {
	score := bson.M{"$add": []interface{}{}}

	// BHK match
	if criteria.BHK > 0 {
		bhkScore := bson.M{
			"$cond": bson.M{
				"if":   bson.M{"$eq": []interface{}{"$bhk", criteria.BHK}},
				"then": 0.1,
				"else": 0,
			},
		}
		score["$add"] = append(score["$add"].([]interface{}), bhkScore)
	}

	// Property type match
	if criteria.PropertyType != "" {
		typeScore := bson.M{
			"$cond": bson.M{
				"if":   bson.M{"$eq": []interface{}{bson.M{"$toLower": "$type"}, criteria.PropertyType}},
				"then": 0.1,
				"else": 0,
			},
		}
		score["$add"] = append(score["$add"].([]interface{}), typeScore)
	}

	// If no specifications, give full score
	if len(score["$add"].([]interface{})) == 0 {
		return bson.M{"$literal": 0.2}
	}

	return score
}

func (re *RecommendationEngine) buildAmenityScore(criteria *SearchCriteria) bson.M {
	if len(criteria.Amenities) == 0 && !criteria.ParkingRequired {
		return bson.M{"$literal": 0.15} // Full score if no amenity preferences
	}

	score := 0.0

	// Parking score
	if criteria.ParkingRequired {
		score += 0.075 // Half of amenity weight for parking
	}

	// Other amenities score
	if len(criteria.Amenities) > 0 {
		score += 0.075 // Half of amenity weight for other amenities
	}

	return bson.M{"$literal": score}
}

func (re *RecommendationEngine) buildTimelineScore(criteria *SearchCriteria) bson.M {
	// Timeline scoring based on urgency
	switch strings.ToLower(criteria.Timeline) {
	case "immediately":
		return bson.M{"$literal": 0.1}
	case "within 1 month":
		return bson.M{"$literal": 0.08}
	case "1-3 months":
		return bson.M{"$literal": 0.06}
	default:
		return bson.M{"$literal": 0.04}
	}
}

// Matching criteria builders

func (re *RecommendationEngine) buildBudgetMatch(criteria *SearchCriteria) bson.M {
	if criteria.BudgetMin == 0 && criteria.BudgetMax == 0 {
		return bson.M{"$literal": true}
	}

	return bson.M{
		"$and": []bson.M{
			{"$gte": []interface{}{"$total_price", criteria.BudgetMin}},
			{"$lte": []interface{}{"$total_price", criteria.BudgetMax}},
		},
	}
}

func (re *RecommendationEngine) buildLocationMatch(criteria *SearchCriteria) bson.M {
	if len(criteria.Locations) == 0 {
		return bson.M{"$literal": true}
	}

	return bson.M{
		"$or": []bson.M{
			{"$in": []interface{}{"$location.city", criteria.Locations}},
			{"$in": []interface{}{"$location.area", criteria.Locations}},
		},
	}
}

func (re *RecommendationEngine) buildSpecMatch(criteria *SearchCriteria) bson.M {
	matches := []bson.M{}

	if criteria.BHK > 0 {
		matches = append(matches, bson.M{"$eq": []interface{}{"$bhk", criteria.BHK}})
	}

	if criteria.PropertyType != "" {
		matches = append(matches, bson.M{"$eq": []interface{}{bson.M{"$toLower": "$type"}, criteria.PropertyType}})
	}

	if len(matches) == 0 {
		return bson.M{"$literal": true}
	}

	return bson.M{"$and": matches}
}

func (re *RecommendationEngine) buildAmenityMatch(criteria *SearchCriteria) bson.M {
	matches := []bson.M{}

	if criteria.ParkingRequired {
		matches = append(matches, bson.M{"$gt": []interface{}{"$no_of_parking", 0}})
	}

	if len(matches) == 0 {
		return bson.M{"$literal": true}
	}

	return bson.M{"$and": matches}
}

// generateMatchReasons creates human-readable match reasons
func (re *RecommendationEngine) generateMatchReasons(property models.Property, criteria *SearchCriteria) []string {
	var reasons []string

	// Budget match
	if criteria.BudgetMin > 0 && criteria.BudgetMax > 0 {
		if property.TotalPrice >= criteria.BudgetMin && property.TotalPrice <= criteria.BudgetMax {
			reasons = append(reasons, fmt.Sprintf("Within your budget range (₹%.0f - ₹%.0f)", criteria.BudgetMin, criteria.BudgetMax))
		}
	}

	// Location match
	for _, loc := range criteria.Locations {
		if strings.EqualFold(property.Location.City, loc) || strings.EqualFold(property.Location.Area, loc) {
			reasons = append(reasons, fmt.Sprintf("Located in your preferred area: %s", loc))
			break
		}
	}

	// BHK match
	if criteria.BHK > 0 && property.BHK == criteria.BHK {
		reasons = append(reasons, fmt.Sprintf("Matches your %d BHK requirement", criteria.BHK))
	}

	// Property type match
	if criteria.PropertyType != "" && strings.EqualFold(property.Type, criteria.PropertyType) {
		reasons = append(reasons, fmt.Sprintf("Matches your preferred property type: %s", criteria.PropertyType))
	}

	// Parking match
	if criteria.ParkingRequired && property.NoOfParking > 0 {
		reasons = append(reasons, "Has parking space as requested")
	}

	return reasons
}

// Helper method to get session responses
func (re *RecommendationEngine) getSessionResponses(ctx context.Context, sessionID primitive.ObjectID) ([]models.UserResponse, error) {
	collection := re.db.GetCollection("user_responses")

	filter := bson.M{"session_id": sessionID}
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var responses []models.UserResponse
	if err := cursor.All(ctx, &responses); err != nil {
		return nil, err
	}

	return responses, nil
}
